using System;
using UdonSharp;
using UnityEngine;
using VRC.SDK3.Components;
using VRC.SDKBase;
using VRC.Udon;
using System.Text;
using TMPro;
using VRC.Udon.Common;
using UnityEngine.UI;
using VRC.SDK3.Persistence;
using VRC.SDK3.UdonNetworkCalling;
using NUnit.Framework;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class GameManager : UdonSharpBehaviour
{
    [UdonSynced] public int currentGameMode = 1; // 0 = Normal, 1 = Barfight Mode
    private int _l_currentGameMode = 1;
    [UdonSynced] public int seatStatus = 0;
    [UdonSynced] public int playerStatus = 0;
    [UdonSynced] public bool gameStarted = false;
    [UdonSynced] public string cardOwnership = ""; // Synced variable to store card ownership

    [SerializeField] public VRC.SDK3.Components.VRCStation[] stations = new VRC.SDK3.Components.VRCStation[4];
    [SerializeField] private GameObject settingsAndStart; // Reference to the start button GameObject
    public bool[] seatStatuses = new bool[4] { false, false, false, false };

    [UdonSynced] public string playerIDs = "";
    private string _l_playerIDs = "";

    public int[] playerIDsArray = new int[4] { -1, -1, -1, -1 };

    [SerializeField] private GameObject[] revolverIndicators = new GameObject[4];

    private PlayerObjectDealer _pod;

    public int tableID = 0;
    private bool _l_gameStarted = false;
    private string _l_cardOwnership = "";


    [SerializeField] public GameObject[] cards = new GameObject[20]; // Array to store references to the 20 cards
    [SerializeField] private GameObject[] cardAnchors = new GameObject[4]; // Array to store references to the 4 card anchors
    [UdonSynced] public int currentRoundRandomValue = -1;

    private int _l_currentRoundRandomValue = -1;

    [SerializeField] private Animator deckAnimator;
    [SerializeField] TextMeshProUGUI roundText;
    [SerializeField] private Animator statusAnimator;
    [SerializeField] TextMeshProUGUI statusText;

    [SerializeField] TextMeshProUGUI gameWinnerText;
    [SerializeField] public GameObject showdownObject;

    [SerializeField] private PlayingCard roundAnimatorCard;
    private bool _l_chairsLocked = false;
    [UdonSynced] public bool gameDraw = false;
    private bool _l_gameDraw = false;
    [UdonSynced] public int gameStatus = 0; // 0 = Not started, 1 = Started, 2 = Cards Dealt, initialization done, 3 = Round in progress, 4 = Round over, 5 = Game over

    private int _l_gameStatus = 0;

    [UdonSynced] public int gameWinner = -1;
    [UdonSynced] public bool gameWinDefault = false;
    private int _l_gameWinner = -1;
    [UdonSynced] public int currentRound = 0;
    [UdonSynced] public int currentRoundPlayer = -1;
    [UdonSynced] public int currentRoundPlayerVersion = 0;

    private int _l_currentRound = 0;
    public int _l_currentRoundPlayer = -1;
    [UdonSynced] public int lastSeatToDie = -1;
    [UdonSynced] public int currentRoundTurn = 0;

    private int _l_currentRoundTurn = 0;

    [SerializeField] GameObject turnIndicatorPointer;

    [UdonSynced] public bool didPreviousPlayerBullshit = false;

    private bool _l_didPreviousPlayerBullshit = false;

    [UdonSynced] public int previousSeatId = -1;
    private int _l_previousSeatId = -1;

    [SerializeField] private Transform deckObjectTransform;

    private int[] lastSubmittedCards = new int[3] { -1, -1, -1 }; // maximum of 3 cards can ever be submitted at a time

    [UdonSynced] public string lastSubmittedCardsString = "";
    private string _l_lastSubmittedCardsString = "";

    [UdonSynced] public int rouletteTarget = -1;
    [UdonSynced] public bool bCorrectCallShowdown = false;
    private bool _l_bCorrectCallShowdown = false;

    private int _l_rouletteTarget = -1;

    [UdonSynced] private int BSCaller = -1;

    [SerializeField] private GameObject[] revealCards = new GameObject[3];

    [UdonSynced] public bool bRevealCards = false;
    private bool _l_bRevealCards = false;
    // [UdonSynced] public int deadPlayerID = -1;

    // private int _l_deadPlayerID = -1;

    [UdonSynced] public string deadPlayerIDs = "";
    private string _l_deadPlayerIDs = "";
    private string[] _dplrIds_string = new string[0];
    private int[] deadPlayerIDsArray = new int[4] { -1, -1, -1, -1 };

    [SerializeField] private AudioSource deckAudio;
    [SerializeField] private AudioSource qdAudio;
    [SerializeField] private AudioClip[] oneShotClips = new AudioClip[3]; // [0] = shuffle, [1] = start of game, [2] = your turn

    [UdonSynced] public int forcedBSCall = -1;

    private int _l_forcedBSCall = -1;

    [SerializeField] private TextMeshProUGUI[] shotsTexts = new TextMeshProUGUI[4];
    [SerializeField] private GameObject[] shotsCanvases = new GameObject[4];
    [UdonSynced] public bool lastRoundQuit = false;

    [UdonSynced] public string devilCardShooters = "";
    [UdonSynced] public int devilCardID = -1;
    [UdonSynced] public int quickdrawCardID = -1;
    [UdonSynced] public bool devilCardEnabled = true;
    [UdonSynced] public bool quickdrawEnabled = true;


    [SerializeField] private Animator devilsCardSwitchAnimator;
    [SerializeField] private Animator quickdrawSwitchAnimator;

    [UdonSynced] public int devilCardshots = 0;
    [UdonSynced] public int devilCardshotsTaken = 0;


    private int _l_devilCardID = -1;
    private string _l_devilCardShooters = "";
    private bool _l_devilCardEnabled = true;

    private int _l_devilCardshots = 0;
    private int _l_devilCardshotsTaken = 0;

    [UdonSynced] public int devilCardKillsThisRound = 0;

    [SerializeField] private ShotCanvasRotator[] shotCanvasRotators = new ShotCanvasRotator[4];
    [SerializeField] private Material myTurnMaterial;
    [SerializeField] private Material notMyTurnMaterial;

    [SerializeField] private GameObject startButton;

    [UdonSynced] public bool fullHouse = false;
    private string suspectPlayerIDs = "";


    private int[] _currentGameKills = new int[4] { 0, 0, 0, 0 }; // kill count by seat ID
    [UdonSynced] private bool devilCardCalled = false;

    [UdonSynced] public int dealVersion = 0;
    private int _l_dealVersion;
    [UdonSynced] double startAllowed = 0;
    private bool startNextRoundTimer;

    private bool randomValueForCardTimer;
    private bool randomStartingPlayerTimer;
    private System.Random rng;

    [UdonSynced] public bool showdownCalled = false;
    private bool _l_showdownCalled = false;

    [UdonSynced] public bool gunslingerCalled = false;
    private bool _l_gunslingerCalled = false;

    [UdonSynced] public int showdownGunsPickedUp = 0;
    private bool[] gunsPickedUpForShowdown = new bool[4] { false, false, false, false };
    [UdonSynced] public double showdownTriggerTime = -1;
    private double _l_showdownTriggerTime = -1;
    [UdonSynced] public int showdownShooters = 0;

    private bool gameOverTimer;
    private bool[] showdownDeaths = new bool[4] { false, false, false, false };
    private int _l_currentRoundPlayerVersion;
    [UdonSynced] public int wantedPlayerIDAtStartOfGame = -1;
    [UdonSynced] public int bountyAmountAtStartOfGame = 0;

    [UdonSynced] public int tableMasterID = -1;
    public VRCPlayerApi _tableMaster = null;
    private VRCPlayerApi localPlayerCache;
    private bool unjammerStarted = false;
    private bool unjammerStop = false;
    private double _l_startAllowed;
    private int thisRoundShowdownDeaths;
    [UdonSynced] private string cumulativeDeaths = "";
    private int _l_quickdrawCardID;
    [UdonSynced] public bool quickdrawCalled = false;
    [UdonSynced] private int quickDrawShotsLeft = 0;
    private bool _l_quickdrawCalled;
    [UdonSynced] public int qdChallengeTarget = -1;
    [UdonSynced] public int qdChallenger = -1;
    [UdonSynced] private double qdDrawTime = -1;
    private int _l_qdChallengeTarget;
    private int _l_qdChallenger;
    private double _l_qdDrawTime;
    [UdonSynced] public bool qdChallengerDead = false;
    [UdonSynced] public bool qdChallengeTargetDead = false;
    private bool _l_qdChallengerDead = false;
    private bool _l_qdChallengeTargetDead = false;

    [SerializeField] private GameObject[] QDHitboxes = new GameObject[4];

    [SerializeField] public Transform qdChallengerSpot;
    [SerializeField] public Transform qdChallengeTargetSpot;
    [SerializeField] private GameObject tumbleWeedObject;

    [SerializeField] private GameObject QDChallengerText;
    [SerializeField] private GameObject QDChallengeTargetText;

    [SerializeField] public Transform QDChallengerGunTransformR;
    [SerializeField] public Transform QDChallengerGunTransformL;

    [SerializeField] public Transform QDChallengeTargetGunTransformR;
    [SerializeField] public Transform QDChallengeTargetGunTransformL;

    [SerializeField] private GameObject QDChallengerHitbox;
    [SerializeField] private GameObject QDChallengeTargetHitbox;
    [SerializeField] private GameObject QDAntiNonsenseHitbox;

    [SerializeField] private Transform QDMoveSpots;

    private VRCPlayerApi _delayedTransferTarget;

    private int[] showdownTargetCounts = new int[4];
    private bool qdAwardTenPaces = false;

    void Start()
    {
        _pod = GameObject.Find("PlayerObjectDealer").GetComponent<PlayerObjectDealer>(); //
        settingsAndStart.SetActive(false); // Hide the start button initially  
        for (var i = 0; i < cards.Length; i++)
        {
            cards[i].GetComponent<PlayingCard>().cardID = i;
        }
        for (var i = 0; i < revolverIndicators.Length; i++)
        {
            revolverIndicators[i].SetActive(false);
            revolverIndicators[i].GetComponent<Animator>().speed = 0;

        }
        for (var i = 0; i < revealCards.Length; i++)
        {
            revealCards[i].GetComponent<PlayingCard>().DisableCardRenderers();
        }

        gameWinner = -1;
        _l_gameWinner = -1;

        SendCustomEventDelayedSeconds("DelayedChairStatusChecker", 1f);

        if (gameStatus == 5 || gameStatus == 0)
        {
            ResetAllCards();
            ResetRevealCards();
            deckAnimator.ResetTrigger("HideCard");
            deckAnimator.SetTrigger("HideCard");
        }

        rng = new System.Random(DateTime.Now.Ticks.GetHashCode());

        VRCPlayerApi[] players = new VRCPlayerApi[VRCPlayerApi.GetPlayerCount()];
        VRCPlayerApi.GetPlayers(players);

        for (var i = 0; i < players.Length; i++)
        {
            if (players[i].isMaster)
            {
                // Debug.Log("<color=green>Master player found: " + players[i].displayName + "</color>");
            }
        }

        localPlayerCache = Networking.LocalPlayer;

        // if (Networking.IsMaster && tableMasterID == -1)
        // {
        //     tableMasterID = localPlayerCache.playerId;
        //     _tableMaster = localPlayerCache;
        //     RequestSerialization();
        // }

        PlayerVincinityLoop();

    }
    public void DelayedChairStatusChecker()
    {
        if (gameStarted)
        {
            // Debug.Log("Game is started, we are not dealing with this");
            for (var i = 0; i < seatStatuses.Length; i++)
            {
                if (!seatStatuses[i])
                {
                    Debug.Log("Disabling chair from DelayedChairStatusChecker");
                    stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().NDisableMe();
                }
                else
                {
                    stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().NEnableMe();
                }
            }
        }
        else
        {
            for (var i = 0; i < stations.Length; i++)
            {
                stations[i].gameObject.transform.parent.GetComponent<PlayerSeat>().NEnableMe();
            }
        }
    }


    public void _OnRelayedStationEnter1()
    {
        // We need to check if the chair being entered is actually unoccupied

        // Debug.Log("RELAYED STATION 1");
        // if (seatStatuses[0] || gameStarted)
        // {
        //     Debug.Log("Seat 0 is already occupied. Ignoring the enter event.");
        //     return;
        // }
        // else
        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, 0);

    }

    public void _OnRelayedStationExit1()
    {
        // Debug.Log("STATION EXIT, IS MASTER: " + _IsLocalPlayerTableMaster());
        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, -1);
    }

    public void _OnRelayedStationEnter2()
    {
        // if (seatStatuses[1] || gameStarted)
        // {
        //     Debug.Log("Seat 1 is already occupied. Ignoring the enter event.");
        //     return;
        // }
        // else
        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, 1);

    }

    public void _OnRelayedStationExit2()
    {
        // Debug.Log("STATION EXIT, IS MASTER: " + _IsLocalPlayerTableMaster());

        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, -1);
    }

    public void _OnRelayedStationEnter3()
    {
        // if (seatStatuses[2] || gameStarted)
        // {
        //     Debug.Log("Seat 2 is already occupied. Ignoring the enter event.");
        //     return;
        // }
        // else
        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, 2);

    }

    public void _OnRelayedStationExit3()
    {
        // Debug.Log("STATION EXIT, IS MASTER: " + _IsLocalPlayerTableMaster());

        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, -1);
    }

    public void _OnRelayedStationEnter4()
    {
        // if (seatStatuses[3] || gameStarted)
        // {
        //     Debug.Log("Seat 3 is already occupied. Ignoring the enter event.");
        //     return;
        // }
        // else
        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, 3);

    }

    public void _OnRelayedStationExit4()
    {
        // Debug.Log("STATION EXIT, IS MASTER: " + _IsLocalPlayerTableMaster());

        _pod.GetLocalPlayerScript().SetTableAndSeatID(tableID, -1);
    }

    public void _ReturnPlayersCards(int seatID)
    {
        // Debug.Log("<color=pink>Returning players cards: " + seatID + "</color>");
        if (seatID == -1) return;
        // from the card ownership string we can figure out which cards belong to this seat
        // we need to return them to the deck
        bool found = false;
        string[] cardValues = cardOwnership.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
        for (var i = 0; i < cardValues.Length; i++)
        {
            int playerID = int.Parse(cardValues[i].Split(new char[] { ':' }, StringSplitOptions.RemoveEmptyEntries)[0]);
            if (playerID == seatID)
            {
                string cardStack = cardValues[i].Split(new char[] { ':' }, StringSplitOptions.RemoveEmptyEntries)[1];

                string[] splitCards = cardStack.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                for (var j = 0; j < splitCards.Length; j++)
                {
                    int cardID = int.Parse(splitCards[j].Split(new char[] { '-' }, StringSplitOptions.RemoveEmptyEntries)[0]);
                    // Debug.Log("Found dudes cards");
                    // cards[cardID].GetComponent<PlayingCard>().ResetCard();
                    found = true;
                    for (var h = 0; h < cards.Length; h++)
                    {
                        if (cards[h].GetComponent<PlayingCard>().cardID == cardID)
                        {
                            cards[h].GetComponent<PlayingCard>()._ResetCard();
                        }
                    }
                }

                break;
            }

            if (found) break;
        }
    }

    private void ValidateCurrentRoundPlayer()
    {
        if (currentRoundPlayer == -1) return;

        // Check if current player is still valid
        if (playerIDsArray[currentRoundPlayer] == -1)
        {
            Debug.Log($"<color=#76f59a>Current round player seat {currentRoundPlayer} is empty, finding next player</color>");
            currentRoundPlayer = FindNextPlayer(currentRoundPlayer);
            RequestSerialization();
            return;
        }

        VRCPlayerApi player = VRCPlayerApi.GetPlayerById(playerIDsArray[currentRoundPlayer]);
        if (player == null || !player.IsValid())
        {
            Debug.Log($"<color=#76f59a>Current round player is invalid, finding next player</color>");
            currentRoundPlayer = FindNextPlayer(currentRoundPlayer);
            RequestSerialization();
            return;
        }

        var seatPlayer = GetSeatPlayerObject(currentRoundPlayer);
        if (seatPlayer != null && seatPlayer.dead)
        {
            Debug.Log($"<color=#76f59a>Current round player is dead, finding next player</color>");
            currentRoundPlayer = FindNextPlayer(currentRoundPlayer);
            RequestSerialization();
        }
    }

    public void _GameStateUnjammer()
    {
        return; // Disabling this for now, its a band-aid fix.

    }

    public void PlayerVincinityLoop()
    {
        // Not ready for production yet, I don't think. 
        return;
        // if (_IsLocalPlayerTableMaster())
        // {
        //     GetAllPlayersInVincinity();

        //     SendCustomEventDelayedSeconds("PlayerVincinityLoop", 5f);
        // }
    }


    internal void EnableQDHitboxes(bool challenged)
    {
        if (challenged)
        {
            QDChallengerHitbox.SetActive(true);
        }
        else
        {
            QDChallengeTargetHitbox.SetActive(true);
        }
    }

    public void _DisableQDHitboxes()
    {
        for (int i = 0; i < QDHitboxes.Length; i++)
        {
            QDHitboxes[i].SetActive(false);
        }
    }

    public override void OnDeserialization()
    {
        // Debug.Log("Received seat status: " + seatStatus);
        // if (gameStatus != 5)
        // {
        UnpackSeatStatuses();
        // }
        // Debug.Log("So, here is the seat statuses unpacked:");

        UpdateStartButtonVisibility(); // Check and update the visibility of the start button

        // if (_tableMaster != null && _tableMaster.playerId != tableMasterID)
        // {
        //     _tableMaster = VRCPlayerApi.GetPlayerById(tableMasterID);
        //     if (_tableMaster != null)
        //     {
        //         Debug.Log("<color=#76f59a>Master player found: " + _tableMaster.displayName + "</color>");
        //         if (_tableMaster.isLocal)
        //         {
        //             Debug.Log("<color=#76f59a>Master player is local player</color>");
        //             Networking.SetOwner(localPlayerCache, gameObject);
        //         }
        //     }
        // }

        if (startAllowed != _l_startAllowed)
        {
            _l_startAllowed = startAllowed;

            SendCustomEventDelayedSeconds("EnableStartButton", (float)((startAllowed - GetNormalizedServerTime()) / 1000));
        }

        if (tableMasterID != -1)
        {
            if (_tableMaster != null && _tableMaster.playerId == tableMasterID)
            {
                if (localPlayerCache.displayName == "aRkker")
                {
                    Debug.Log("Master player is already set correctly.");
                }

                if (Networking.GetOwner(gameObject) != _tableMaster)
                {
                    Networking.SetOwner(_tableMaster, gameObject);
                }
            }
            else
            {
                if (_tableMaster == null)
                {
                    _tableMaster = VRCPlayerApi.GetPlayerById(tableMasterID);
                }
                else if (_tableMaster.playerId != tableMasterID)
                {
                    _tableMaster = VRCPlayerApi.GetPlayerById(tableMasterID);
                }

                if (_tableMaster != null)
                {
                    if (localPlayerCache.displayName == "aRkker")
                        Debug.Log("<color=#76f59a>Master player changed, and found: " + _tableMaster.displayName + "</color>");

                    if (_tableMaster.isLocal)
                    {
                        if (localPlayerCache.displayName == "aRkker")
                            Debug.Log("<color=#76f59a>Master player is local player</color>");
                        Networking.SetOwner(localPlayerCache, gameObject);
                    }
                }
                else
                {
                    if (localPlayerCache.displayName == "aRkker")
                        Debug.Log("<color=red>Master player not found</color>");
                    _tableMaster = null;
                    tableMasterID = -1;
                }
            }

        }

        if (_l_gameStarted != gameStarted)
        {
            _l_gameStarted = gameStarted;
            // Debug.Log("GAME STARTED? " + gameStarted);
            if (gameStarted)
            {
                if (IsLocalPlayerSeatedAtThisTable())
                {
                    // Debug.Log("<color=green>Hidebuttons from GameStarted</color>");
                    _pod.GetLocalPlayerScript().HideButtons();
                }
                deckAnimator.ResetTrigger("HideCard");
                OnGameStarted();
            }
        }
        // Debug.Log("Game winner status: " + gameWinner);
        if (lastSubmittedCards != null && _l_lastSubmittedCardsString != lastSubmittedCardsString)
        {
            _l_lastSubmittedCardsString = lastSubmittedCardsString;

            if (Utilities.IsValid(_l_lastSubmittedCardsString))
            {
                if (_l_lastSubmittedCardsString.Length > 0 && _l_lastSubmittedCardsString.Contains(";"))
                {
                    string[] cardValues = lastSubmittedCardsString
                        .Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                    lastSubmittedCards = new int[cardValues.Length];
                    for (int i = 0; i < cardValues.Length; i++)
                    {
                        lastSubmittedCards[i] = int.Parse(cardValues[i]);
                    }
                }
            }
        }

        if (currentGameMode != _l_currentGameMode)
        {
            // Debug.Log("GAME MODE CHANGED: " + currentGameMode);
            _l_currentGameMode = currentGameMode;
            if (currentGameMode == 1)
            {
                settingsAndStart.GetComponent<TableSettings>().EnableShowdownMode();
            }
            else
            {
                settingsAndStart.GetComponent<TableSettings>().EnableClassicMode();
            }
        }

        // if (devilCardEnabled != _l_devilCardEnabled)
        // {
        //     _l_devilCardEnabled = devilCardEnabled;
        //     devilsCardSwitchAnimator.SetBool("SwitchOn", devilCardEnabled);
        // }
        devilsCardSwitchAnimator.SetBool("SwitchOn", devilCardEnabled);
        quickdrawSwitchAnimator.SetBool("SwitchOn", quickdrawEnabled);

        if (_l_playerIDs != playerIDs)
        {
            if (Utilities.IsValid(playerIDs) && Utilities.IsValid(_l_playerIDs))
            {
                // Debug.Log("Received new player IDs: " + playerIDs);
                // We have to figure out if there are players that left the table
                string[] oldIDs = _l_playerIDs.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                string[] newIDs = playerIDs.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                for (var i = 0; i < oldIDs.Length; i++)
                {
                    if (oldIDs[i].Length < 1) continue;
                    int oldID = int.Parse(oldIDs[i]);
                    if (Array.IndexOf(newIDs, oldID.ToString()) == -1)
                    {
                        // Debug.Log("Player left: " + oldID);

                        _ReturnPlayersCards(oldID);

                    }
                }


                DeserializePlayerIDs();
                _l_playerIDs = playerIDs;

            }
        }

        if (gameDraw != _l_gameDraw)
        {
            _l_gameDraw = gameDraw;

            if (gameDraw && gameWinner == -1 && gameStatus == 5)
            {
                // Debug.Log("Serialization draw!");
                deckAudio.Stop();
                deckAudio.PlayOneShot(oneShotClips[8]);
                gameWinnerText.transform.parent.gameObject.SetActive(true);
                gameWinnerText.text = "Draw!";

                for (var i = 0; i < cards.Length; i++)
                {
                    cards[i].GetComponent<PlayingCard>()._ResetCard();
                }
                for (var i = 0; i < revealCards.Length; i++)
                {
                    revealCards[i].GetComponent<PlayingCard>()._ResetCard();
                }

                if (IsLocalPlayerSeatedAtThisTable())
                    _pod.GetLocalPlayerScript().GameOver();

                for (var i = 0; i < stations.Length; i++)
                {
                    stations[i].gameObject.GetComponent<Collider>().enabled = true;
                }
                deckAnimator.ResetTrigger("HideCard");
                deckAnimator.SetTrigger("HideCard");
                // Debug.Log("<color=#FF00FF>HideCard from GameOver OnDeserialization, draw section</color>");
            }
        }

        // if (qdChallengerDead != _l_qdChallengerDead)
        // {
        //     _l_qdChallengerDead = qdChallengerDead;

        //     if (gameStarted && IsLocalPlayerSeatedAtThisTable() && qdChallengerDead)
        //     {
        //         Debug.Log("QD Challenger is dead!");
        //         if (qdChallenger == GetLocalPlayerSeatID() && !qdChallengeTargetDead)
        //         {
        //             _pod.GetLocalPlayerScript().SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
        //         }
        //     }
        // }

        // if (qdChallengeTargetDead != _l_qdChallengeTargetDead)
        // {
        //     _l_qdChallengeTargetDead = qdChallengeTargetDead;
        //     if (gameStarted && IsLocalPlayerSeatedAtThisTable() && qdChallengeTargetDead)
        //     {
        //         Debug.Log("QD Target is dead!");
        //         if (qdChallengeTarget == GetLocalPlayerSeatID() && !qdChallengerDead)
        //         {
        //             _pod.GetLocalPlayerScript()._Die();
        //         }
        //     }
        // }

        if (qdDrawTime != _l_qdDrawTime)
        {
            _l_qdDrawTime = qdDrawTime;

            Debug.Log("New QD Draw time: " + qdDrawTime + ". Calculated time: " + (qdDrawTime - GetNormalizedServerTime()));

            // Calculate when we need to call the PlayFireSound method
            if (qdDrawTime != -1 && qdDrawTime > 0 && quickdrawCalled)
            {
                SendCustomEventDelayedFrames("SetupFireSound", 1);
            }
        }

        if ((qdChallengeTarget != _l_qdChallengeTarget || qdChallenger != _l_qdChallenger) && quickdrawCalled)
        {
            _l_qdChallengeTarget = qdChallengeTarget;
            _l_qdChallenger = qdChallenger;

            if (qdChallengeTarget != -1 && qdChallenger != -1)
            {
                // deckAudio.Stop();
                // deckAudio.PlayOneShot(oneShotClips[10]);
                qdAudio.PlayOneShot(oneShotClips[10]);
                deckAnimator.ResetTrigger("HideCard");
                deckAnimator.SetTrigger("HideCard");
                // Debug.Log("Quickdraw challenge! Challenger: " + qdChallenger + ", Target: " + qdChallengeTarget);
                // CheckIfLocalPlayerIsInvolvedInQD();
            }
        }



        if (_l_gameWinner != gameWinner)
        {
            Debug.Log($"Winner changed: Old = {_l_gameWinner}, New = {gameWinner}. Draw: {gameDraw}");
            _l_gameWinner = gameWinner;
            if (gameWinner != -1 && !gameDraw)
            {
                gameStatus = 5;

                Debug.Log("Game over! The winner is seat " + gameWinner);
                // we need to display the winner
                // _DisplayStatusMessage("Game over! The winner is " + Networking.GetOwner(stations[gameWinner].gameObject).displayName);
                gameWinnerText.transform.parent.gameObject.SetActive(true);
                gameWinnerText.text = "Winner\n" + Networking.GetOwner(stations[gameWinner].transform.parent.gameObject).displayName;
                deckAudio.Stop();
                deckAudio.PlayOneShot(oneShotClips[4]);
                for (var i = 0; i < cards.Length; i++)
                {
                    cards[i].GetComponent<PlayingCard>()._ResetCard();
                }
                for (var i = 0; i < revealCards.Length; i++)
                {
                    revealCards[i].GetComponent<PlayingCard>()._ResetCard();
                }


                if (IsLocalPlayerSeatedAtThisTable())
                {
                    // Debug.Log("We are at this table! our seat ID: " + GetLocalPlayerSeatID());
                    if (gameWinner == GetLocalPlayerSeatID())
                    {
                        // Debug.Log("We won!");
                        _pod.GetLocalPlayerScript().WinGame();
                    }
                    _pod.GetLocalPlayerScript().GameOver();
                }
                else
                {
                    // Debug.Log("we are not at this table :)");
                }

                for (var i = 0; i < stations.Length; i++)
                {
                    stations[i].gameObject.GetComponent<Collider>().enabled = true;
                }
                deckAnimator.ResetTrigger("HideCard");
                deckAnimator.SetTrigger("HideCard");
                // Debug.Log("<color=#FF00FF>HideCard from GameOver OnDeserialization, gameWinner section</color>");

                // if (_IsLocalPlayerTableMaster())
                // {
                //     _pod.GetPlayerScript(Networking.GetOwner(stations[gameWinner].transform.parent.gameObject)).WinGame();
                // }


            }
            else if (gameWinner == -1 && gameDraw)
            {
                // Debug.Log("Serialization draw!");
                deckAudio.Stop();
                deckAudio.PlayOneShot(oneShotClips[8]);
                gameWinnerText.transform.parent.gameObject.SetActive(true);
                gameWinnerText.text = "Draw!";

                for (var i = 0; i < cards.Length; i++)
                {
                    cards[i].GetComponent<PlayingCard>()._ResetCard();
                }
                for (var i = 0; i < revealCards.Length; i++)
                {
                    revealCards[i].GetComponent<PlayingCard>()._ResetCard();
                }

                if (IsLocalPlayerSeatedAtThisTable())
                    _pod.GetLocalPlayerScript().GameOver();

                for (var i = 0; i < stations.Length; i++)
                {
                    stations[i].gameObject.GetComponent<Collider>().enabled = true;
                }
                deckAnimator.ResetTrigger("HideCard");
                gameStatus = 5;
                deckAnimator.SetTrigger("HideCard");
                // Debug.Log("<color=#FF00FF>HideCard from GameOver OnDeserialization, draw section</color>");
            }
        }

        if (_l_forcedBSCall != forcedBSCall)
        {
            _l_forcedBSCall = forcedBSCall;
            if (forcedBSCall != -1)
            {
                // // Debug.Log("Forced BS call by seat " + forcedBSCall);
                if (forcedBSCall == GetLocalPlayerSeatID() && IsLocalPlayerSeatedAtThisTable())
                {
                    _pod.GetLocalPlayerScript().CallBS(true);
                }
            }
        }

        if (showdownCalled != _l_showdownCalled)
        {
            _l_showdownCalled = showdownCalled;
            if (showdownCalled)
            {
                if (IsLocalPlayerSeatedAtThisTable())
                {
                    _pod.GetLocalPlayerScript().StartShowdown();
                    revolverIndicators[GetLocalPlayerSeatID()].SetActive(true);
                    revolverIndicators[GetLocalPlayerSeatID()].GetComponent<Animator>().speed = 1;

                    ResetAllCards();
                }
            }
        }

        if (quickdrawCalled != _l_quickdrawCalled)
        {
            _l_quickdrawCalled = quickdrawCalled;

            if (quickdrawCalled)
            {
                deckAnimator.ResetTrigger("HideCard");
                deckAnimator.SetTrigger("HideCard");

                RotateTurnIndicator(previousSeatId);
                if (IsLocalPlayerSeatedAtThisTable() && previousSeatId == GetLocalPlayerSeatID())
                {
                    ChangeIndicatorToMyColour();
                }
                else if (IsLocalPlayerSeatedAtThisTable() && previousSeatId != GetLocalPlayerSeatID())
                {
                    ChangeIndicatorToNotMyColour();
                }
                else
                {
                    ChangeIndicatorToMyColour();
                }

                if (IsLocalPlayerSeatedAtThisTable())
                {
                    if (previousSeatId == GetLocalPlayerSeatID())
                    {
                        _pod.GetLocalPlayerScript().PrepareQuickdraw();
                        // revolverIndicators[GetLocalPlayerSeatID()].SetActive(true);
                        // revolverIndicators[GetLocalPlayerSeatID()].GetComponent<Animator>().speed = 1;
                    }

                    ResetAllCards();

                }
            }
        }

        if (gunslingerCalled != _l_gunslingerCalled)
        {
            _l_gunslingerCalled = gunslingerCalled;
            if (gunslingerCalled)
            {
                RotateTurnIndicator(previousSeatId);
                if (IsLocalPlayerSeatedAtThisTable() && previousSeatId == GetLocalPlayerSeatID())
                {
                    ChangeIndicatorToMyColour();
                }
                else if (IsLocalPlayerSeatedAtThisTable() && previousSeatId != GetLocalPlayerSeatID())
                {
                    ChangeIndicatorToNotMyColour();
                }
                else
                {
                    ChangeIndicatorToMyColour();
                }

                if (IsLocalPlayerSeatedAtThisTable())
                {
                    if (previousSeatId == GetLocalPlayerSeatID())
                    {
                        // Debug.Log("Oh boy! They took the bait. Lets go!");
                        _pod.GetLocalPlayerScript().StartGunslinger();
                        revolverIndicators[GetLocalPlayerSeatID()].SetActive(true);
                        revolverIndicators[GetLocalPlayerSeatID()].GetComponent<Animator>().speed = 1;

                    }
                }
            }
        }

        if (cardOwnership != _l_cardOwnership)
        {
            _l_cardOwnership = cardOwnership;
            // if (cardOwnership.Length > 0)
            // {
            //     // Debug.Log("Received card ownership: " + cardOwnership);
            //     DistributeCardsToPlayers();
            //     deckAudio.Play();
            // }

            // if (IsLocalPlayerSeatedAtThisTable())
            //     _pod.GetLocalPlayerScript().CheckIfMyTurn(forcedBSCall != -1);
        }

        if (dealVersion != _l_dealVersion)
        {
            // Debug.Log("Deal version changed: " + dealVersion + ", card ownership: " + cardOwnership);
            DistributeCardsToPlayers();
            deckAudio.Play();
            _l_dealVersion = dealVersion;
        }



        // if (deadPlayerIDs != _l_deadPlayerIDs)
        // {
        //     Debug.Log("Got dead player IDs: " + deadPlayerIDs);
        //     _l_deadPlayerIDs = deadPlayerIDs;

        //     // lets parse the ID string into an array. It is just numbers delimited by a ;
        //     _dplrIds_string = _l_deadPlayerIDs.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
        //     ParseDeadPlayerIDs(_dplrIds_string);
        //     if (Array.IndexOf(deadPlayerIDsArray, localPlayerCache.playerId) != -1 && IsLocalPlayerSeatedAtThisTable() && !_pod.GetLocalPlayerScript().dead)
        //     {
        //         Debug.Log("Calling die from GM deadPlayerID");
        //         _pod.GetLocalPlayerScript()._Die();
        //     }
        // }

        // if (_l_chairsLocked != chairsLocked)
        // {
        //     _l_chairsLocked = chairsLocked;

        //     if (chairsLocked)
        //     {
        //         for (var i = 0; i < stations.Length; i++)
        //         {
        //             stations[i].gameObject.GetComponent<Collider>().enabled = false;
        //             stations[i].enabled = false;
        //         }
        //     }
        //     else
        //     {
        //         for (var i = 0; i < stations.Length; i++)
        //         {
        //             stations[i].enabled = true;
        //             stations[i].gameObject.GetComponent<Collider>().enabled = true;
        //         }
        //     }
        // }

        if (bRevealCards != _l_bRevealCards)
        {
            Debug.Log("Reveal cards mismatch, calling nRevealCards: " + bRevealCards);
            _l_bRevealCards = bRevealCards;
            if (bRevealCards)
            {
                NRevealLastCards();
            }
            else
            {
                ResetRevealCards();
            }
        }

        if (currentRoundRandomValue != _l_currentRoundRandomValue)
        {
            // Debug.Log("[TABLE ID: " + tableID + "] Random card value changed: Old = " + _l_currentRoundRandomValue + ", New = " + currentRoundRandomValue);
            _l_currentRoundRandomValue = currentRoundRandomValue;
            if (currentRoundRandomValue != -1 && gameStarted && gameStatus != 5)
            {
                // Debug.Log("Random card value for this round is " + currentRoundRandomValue);
                PlayAnimationForRandomCardValue();
            }
            else if (currentRoundRandomValue == -1)
            {

            }
        }

        if (bCorrectCallShowdown != _l_bCorrectCallShowdown)
        {
            _l_bCorrectCallShowdown = bCorrectCallShowdown;
        }

        if (_l_rouletteTarget != rouletteTarget)
        {
            _l_rouletteTarget = rouletteTarget;
            if (rouletteTarget != -1)
            {
                // Debug.Log("Roulette target is " + rouletteTarget);
                if (rouletteTarget == GetLocalPlayerSeatID() && IsLocalPlayerSeatedAtThisTable())
                {
                    if (currentGameMode == 1 && bCorrectCallShowdown)
                    {
                        // Debug.Log("We get to pick gunslinger style!");
                        _pod.GetLocalPlayerScript().StartGunslinger();
                        revolverIndicators[GetLocalPlayerSeatID()].SetActive(true);
                        revolverIndicators[GetLocalPlayerSeatID()].GetComponent<Animator>().speed = 1;

                    }
                    else
                    {
                        // Debug.Log("Shit... we need to roulette..");
                        _pod.GetLocalPlayerScript().RussianRoulette();
                        revolverIndicators[GetLocalPlayerSeatID()].SetActive(true);
                        revolverIndicators[GetLocalPlayerSeatID()].GetComponent<Animator>().speed = 1;
                    }


                }
                RotateTurnIndicator(rouletteTarget);

                if (IsLocalPlayerSeatedAtThisTable() && rouletteTarget == GetLocalPlayerSeatID())
                {
                    ChangeIndicatorToMyColour();
                }
                else if (IsLocalPlayerSeatedAtThisTable() && rouletteTarget != GetLocalPlayerSeatID())
                {
                    ChangeIndicatorToNotMyColour();
                }
                else
                {
                    ChangeIndicatorToMyColour();
                }
            }
        }

        if (_l_devilCardShooters != devilCardShooters)
        {
            // Debug.Log("NEW SHOOTERS: " + devilCardShooters);
            _l_devilCardShooters = devilCardShooters;

            if (devilCardShooters != "" && devilCardEnabled)
            {
                string[] shooterIDs = devilCardShooters.Split(";");
                for (var i = 0; i < shooterIDs.Length; i++)
                {
                    if (shooterIDs[i].Length < 1) continue;

                    int iSID = int.Parse(shooterIDs[i]);

                    if (iSID == GetLocalPlayerSeatID() && IsLocalPlayerSeatedAtThisTable())
                    {
                        // Debug.Log("Shit... we need to roulette..");
                        _pod.GetLocalPlayerScript().RussianRoulette();
                        revolverIndicators[GetLocalPlayerSeatID()].SetActive(true);
                        revolverIndicators[GetLocalPlayerSeatID()].GetComponent<Animator>().speed = 1;

                    }
                }
            }
        }

        if (gameStatus != _l_gameStatus)
        {
            // If 5, we need to reset everything.

            if (gameStatus == 5)
            {
                // Debug.Log("Game is over! RESET");
                // GameOver();
                if (gameOverTimer != true)
                {
                    gameOverTimer = true;
                    SendCustomEventDelayedSeconds("GameOver", 6f);
                }
                if (IsLocalPlayerSeatedAtThisTable())
                {
                    if (GetLocalPlayerSeatID() != -1)
                    {
                        var localPlayerSeatObject = GetSeatPlayerObject(GetLocalPlayerSeatID());
                        if (localPlayerSeatObject != null)
                        {
                            localPlayerSeatObject.HideTimers();
                            // Debug.Log("<color=green>HideButtons from GameStatus 5</color>");
                            localPlayerSeatObject.HideButtons();
                            localPlayerSeatObject.HideTutorials();
                        }

                    }
                }
            }

            if (gameStatus == 3)
            {
                if (currentRoundPlayer != -1)
                {
                    turnIndicatorPointer.SetActive(true);
                    if (!IsLocalPlayerSeatedAtThisTable() || currentRoundPlayer == GetLocalPlayerSeatID())
                    {
                        turnIndicatorPointer.GetComponent<Renderer>().material = myTurnMaterial;
                    }
                    else
                    {
                        turnIndicatorPointer.GetComponent<Renderer>().material = notMyTurnMaterial;
                    }
                    RotateTurnIndicator();
                }
                else
                {
                    turnIndicatorPointer.SetActive(false);
                }

                if (IsLocalPlayerSeatedAtThisTable())
                {
                    // Debug.Log("MyTurnCheck from GameStatus 3");
                    _pod.GetLocalPlayerScript().CheckIfMyTurn(forcedBSCall != -1);

                }
            }

            if (gameStatus == 0)
            {
                ResetAllCards();
                ResetRevealCards();
                deckAnimator.ResetTrigger("HideCard");
                deckAnimator.SetTrigger("HideCard");
            }


            _l_gameStatus = gameStatus;
        }

        if (currentRound != _l_currentRound)
        {
            _l_currentRound = currentRound;
            _l_currentRoundPlayer = -1;
            // Debug.Log("New round, we need to give back our cards. Current round: " + currentRound);

            turnIndicatorPointer.SetActive(false);
            // Debug.Log($"<color=#FF00FF>HideCard from currentRound OnDeserialization, roundSection</color>");
            deckAnimator.SetTrigger("HideCard");
            ResetAllCards();
            ResetRevealCards();
            if (gameWinner == -1)
            {
                if (IsLocalPlayerSeatedAtThisTable())
                    _pod.GetLocalPlayerScript().PrepareNextRound();

                if (!_IsLocalPlayerTableMaster())
                {
                    // Debug.Log("Game status: " + gameStatus + " current round: " + _l_currentRound + " ");
                    if (gameStatus != 5 && gameStarted && _l_currentRound != -1)
                    {
                        deckAudio.Stop();
                        deckAudio.PlayOneShot(oneShotClips[3]);
                    }
                }
            }
            else
            {
                // Debug.Log("game Winner is set... not doing anything?");
            }

        }

        if (_l_currentRoundTurn != currentRoundTurn)
        {
            // Debug.Log("NEW TURN! Hiding indicator for now..");
            _l_currentRoundTurn = currentRoundTurn;
            // turnIndicatorPointer.SetActive(false);
            if (currentRoundTurn != -1 && gameStatus == 3)
            {
                if (IsLocalPlayerSeatedAtThisTable())
                {
                    // Debug.Log("MyTurnCheck from currentRoundTurn");
                    _pod.GetLocalPlayerScript().CheckIfMyTurn(forcedBSCall != -1);
                }
            }
        }

        if (currentRoundPlayer != _l_currentRoundPlayer)
        {
            _l_currentRoundPlayer = currentRoundPlayer;
            // Debug.Log("The current round player is " + currentRoundPlayer);
            if (currentRoundPlayer != -1 && gameStatus == 3)
            {
                turnIndicatorPointer.SetActive(true);
                if (!IsLocalPlayerSeatedAtThisTable() || currentRoundPlayer == GetLocalPlayerSeatID())
                {
                    turnIndicatorPointer.GetComponent<Renderer>().material = myTurnMaterial;
                }
                else
                {
                    turnIndicatorPointer.GetComponent<Renderer>().material = notMyTurnMaterial;
                }
                RotateTurnIndicator();
            }
            else
            {
                turnIndicatorPointer.SetActive(false);
            }

            if (IsLocalPlayerSeatedAtThisTable() && gameStatus == 3)
            {
                // Debug.Log("MyTurnCheck from currentRoundPlayer");
                _pod.GetLocalPlayerScript().CheckIfMyTurn(forcedBSCall != -1);
            }

        }

        if (currentRoundPlayerVersion != _l_currentRoundPlayerVersion)
        {
            _l_currentRoundPlayerVersion = currentRoundPlayerVersion;

            // Force the local seat to re-check turn ownership:
            if (IsLocalPlayerSeatedAtThisTable() && gameStatus == 3)
            {
                // Debug.Log("MyTurnCheck from currentRoundPlayerVersion");
                _pod.GetLocalPlayerScript().CheckIfMyTurn(forcedBSCall != -1);
            }
        }

        if (_l_devilCardID != devilCardID && devilCardEnabled)
        {
            // Debug.Log("Showing devil card for this round: " + devilCardID);

            _l_devilCardID = devilCardID;
            if (IsLocalPlayerSeatedAtThisTable() && !_pod.GetLocalPlayerScript().dead)
                SendCustomEventDelayedSeconds("DelayedDevilCardCheck", 1);

        }

        if (_l_quickdrawCardID != quickdrawCardID && quickdrawEnabled && currentGameMode == 1)
        {
            _l_quickdrawCardID = quickdrawCardID;
            if (IsLocalPlayerSeatedAtThisTable() && !_pod.GetLocalPlayerScript().dead)
                SendCustomEventDelayedSeconds("DelayedQuickdrawCheck", 1);
        }

        if (_l_showdownTriggerTime != showdownTriggerTime)
        {
            _l_showdownTriggerTime = showdownTriggerTime;
            // Debug.Log("Showdown trigger time changed: " + showdownTriggerTime);
            if (showdownTriggerTime != -1 && IsLocalPlayerSeatedAtThisTable())
            {
                // Debug.Log("We need to schedule!");
                if (showdownTriggerTime > GetNormalizedServerTime())
                {
                    // Debug.Log("Scheduled time is in the future, we need to schedule the trigger pull.");
                    // Debug.Log("It happens in " + ((showdownTriggerTime - GetNormalizedServerTime()) / 1000) + " seconds.");
                    // Schedule trigger pull in the number of seconds the difference is between the current time and the showdown trigger time
                    _pod.GetLocalPlayerScript().ScheduleTriggerPull((showdownTriggerTime - GetNormalizedServerTime()) / 1000);
                    _SetupTimerForTriggerPullLocally();
                }
            }
            if (showdownTriggerTime != -1)
            {
                showdownObject.SetActive(false);
            }
        }
    }

    // public void CheckIfLocalPlayerIsInvolvedInQD()
    // {
    //     if (IsLocalPlayerSeatedAtThisTable() && _l_qdChallengeTarget == GetLocalPlayerSeatID() && quickdrawCalled && qdDrawTime != -1 && (qdDrawTime - GetNormalizedServerTime()) > 0)
    //     {
    //         // Debug.Log("We are the target of the challenge!");
    //         _EnableQDHitboxes();
    //         _pod.GetLocalPlayerScript()._IAmChallengedForQD(GetSeatPlayerObject(qdChallenger).Owner.displayName, qdDrawTime);
    //     }
    //     if (IsLocalPlayerSeatedAtThisTable() && _l_qdChallenger == GetLocalPlayerSeatID() && quickdrawCalled && qdDrawTime != -1 && (qdDrawTime - GetNormalizedServerTime()) > 0)
    //     {
    //         // Debug.Log("We are the challenger!");
    //         _EnableQDHitboxes();
    //         _pod.GetLocalPlayerScript()._IAmChallengingForQD(GetSeatPlayerObject(qdChallengeTarget).Owner.displayName, qdDrawTime);
    //     }
    // }

    public void SetupFireSound()
    {
        double timeToCall = qdDrawTime - GetNormalizedServerTime();
        SendCustomEventDelayedSeconds("PlayFireSound", (float)timeToCall / 1000);
    }

    private void ParseDeadPlayerIDs(string[] stringedIDs)
    {
        return; // DEPRECATED IN FAVOR OF NETWORK CALLABLE FUNCTIONS
        // Reset the array first
        // for (int i = 0; i < deadPlayerIDsArray.Length; i++)
        // {
        //     deadPlayerIDsArray[i] = -1;
        // }

        // if (stringedIDs == null || stringedIDs.Length == 0)
        // {
        //     return;
        // }

        // // Process each player ID in the string array
        // for (int i = 0; i < stringedIDs.Length; i++)
        // {
        //     if (stringedIDs[i].Length > 0)
        //     {
        //         int playerId = int.Parse(stringedIDs[i]);

        //         if (playerId == -1)
        //             continue;

        //         // Find which seat this player ID belongs to
        //         int seatId = GetSeatIDForPlayer(playerId);

        //         if (seatId != -1)
        //         {
        //             // Store the player ID in the correct seat position in the array
        //             deadPlayerIDsArray[seatId] = playerId;

        //             // Get the player and mark them as dead
        //             VRCPlayerApi player = VRCPlayerApi.GetPlayerById(playerId);
        //             if (Utilities.IsValid(player))
        //             {
        //                 var playerScript = _pod.GetPlayerScript(player);
        //                 if (Utilities.IsValid(playerScript))
        //                 {
        //                     Debug.Log("Setting death for player ID " + playerId + ", seat ID: " + seatId);
        //                     if (playerScript.Owner.isLocal)
        //                     {
        //                         playerScript._Die();
        //                     }
        //                     playerScript.dead = true;

        //                     // Update seat status
        //                     playerIDsArray[seatId] = -1;
        //                     seatStatuses[seatId] = false;
        //                 }
        //             }
        //         }
        //     }
        // }

    }

    private void _SetupTimerForTriggerPullLocally()
    {
        _pod.GetLocalPlayerScript().StartShowdownGunFireTimer(showdownTriggerTime);

    }

    public void DelayedDevilCardCheck()
    {
        _pod.GetLocalPlayerScript().CheckIfWeOwnDevilCard();
    }

    public void DelayedQuickdrawCheck()
    {
        _pod.GetLocalPlayerScript().CheckIfWeOwnQuickdrawCard();
    }

    private void ResetRevealCards()
    {
        for (var i = 0; i < revealCards.Length; i++)
        {
            revealCards[i].GetComponent<PlayingCard>()._ResetCard();
        }
    }


    public void ResetAllCards()
    {
        for (var i = 0; i < cards.Length; i++)
        {
            cards[i].GetComponent<PlayingCard>()._ResetCard();
        }
    }

    public void RotateTurnIndicator(int targetSeatID = -1)
    {
        float rotationZ = 0f;

        // Array of rotation values for each seat
        float[] rotationValues = { 0f, 180f, 90f, -90f };

        if (targetSeatID != -1)
        {
            if (targetSeatID >= 0 && targetSeatID < rotationValues.Length)
            {
                rotationZ = rotationValues[targetSeatID];
            }
        }
        else
        {
            // Adjust rotation to match seat orientation
            if (_l_currentRoundPlayer >= 0 && _l_currentRoundPlayer < rotationValues.Length)
            {
                rotationZ = rotationValues[_l_currentRoundPlayer];
            }
        }

        // if (IsLocalPlayerSeatedAtThisTable() && gameStatus == 3)
        // {
        //     Debug.Log("MyTurnCheck from RotateTurnIndicator");
        //     _pod.GetLocalPlayerScript().CheckIfMyTurn(forcedBSCall != -1);
        // }

        // Set the rotation to point to the appropriate seat :D 
        turnIndicatorPointer.transform.localRotation = Quaternion.Euler(90, 0, rotationZ);
        //
    }

    public void PlayAnimationForRandomCardValue()
    {
        if (currentRoundRandomValue == -1)
        {
            // Debug.LogError("Random card value for this round is not set.");
            return;
        }

        // 0 = Queen, 1 = King, 2 = Ace
        if (currentRoundRandomValue == 0)
        {
            roundText.text = "Queen's Table";
        }
        else if (currentRoundRandomValue == 1)
        {
            roundText.text = "King's Table";
        }
        else if (currentRoundRandomValue == 2)
        {
            roundText.text = "Ace's Table";
        }
        else
        {
            // Debug.LogError("Invalid random card value for this round: " + currentRoundRandomValue);
        }

        // we'll set the cards properties here. suit can be random
        var randomSuit = UnityEngine.Random.Range(0, 3);
        roundAnimatorCard.SetCardProperties(currentRoundRandomValue, randomSuit);
        roundAnimatorCard.UpdateCardTexture();
        roundAnimatorCard._EnableCardRenderers();

        deckAnimator.ResetTrigger("HideCard");
        deckAnimator.ResetTrigger("ShowCard");
        deckAnimator.SetTrigger("ShowCard");

    }

    public override void OnPlayerRespawn(VRCPlayerApi player)
    {
        // Debug.Log("Player respawned: " + player.displayName + ". Am I the master of this table? " + _IsLocalPlayerTableMaster());
        if (_IsLocalPlayerTableMaster())
        {
            for (var i = 0; i < playerIDsArray.Length; i++)
            {
                if (playerIDsArray[i] == player.playerId)
                {
                    // Debug.Log("Player found, lets quit them");
                    _QuitPlayer(i, player.playerId);
                }
            }
        }
        // Lets find if this player was in our table
    }

    public override void OnPlayerLeft(VRCPlayerApi player)
    {
        // // base.OnPlayerLeft(player);

        // if (_IsLocalPlayerTableMaster())
        // {
        //     // we need to check all the chairs, and disable them if there are seats that are not occupied
        //     if (gameStarted && gameStatus != 5)
        //     {
        //         for (var i = 0; i < playerIDsArray.Length; i++)
        //         {
        //             if (playerIDsArray[i] == player.playerId)
        //             {
        //                 Debug.Log("Setting seat " + i + " to unoccupied. This is the player that left: " + player.displayName);
        //                 _SetSeatStatus(i, false, -1);
        //                 currentRoundPlayer = -1;
        //                 currentRoundPlayerVersion++;

        //                 currentRoundTurn = 0;
        //                 currentRound++;
        //                 devilCardCalled = false;
        //                 lastRoundQuit = true;
        //                 BSCaller = -1;
        //                 forcedBSCall = -1;
        //                 previousSeatId = -1;
        //                 rouletteTarget = -1;
        //                 didPreviousPlayerBullshit = false;
        //                 bRevealCards = false;
        //                 _l_bRevealCards = false;
        //                 RequestSerialization();
        //                 OnDeserialization();

        //                 if (!startNextRoundTimer)
        //                 {
        //                     startNextRoundTimer = true;
        //                     SendCustomEventDelayedSeconds("StartNextRound", 3f);
        //                 }
        //             }
        //         }
        //     }

        // }

        if (_IsLocalPlayerTableMaster())
        {
            for (var i = 0; i < playerIDsArray.Length; i++)
            {
                if (playerIDsArray[i] == player.playerId)
                {
                    // Debug.Log("Player found, lets quit them");
                    _QuitPlayer(i, player.playerId);
                }
            }
        }

        _CheckTablePlayers();
    }

    private void _CheckTablePlayers()
    {
        if (_IsLocalPlayerTableMaster())
        {
            int playersLeft = 0;
            for (var i = 0; i < playerIDsArray.Length; i++)
            {
                if (playerIDsArray[i] != -1)
                {
                    if (Utilities.IsValid(VRCPlayerApi.GetPlayerById(playerIDsArray[i])))
                    {
                        playersLeft++;
                    }
                }
            }

            if (playersLeft == 0 && gameStarted)
            {
                Debug.Log("All players left the table. Resetting the table.");
                _LogDumpGameOver();
            }
        }
    }

    public void PackSeatStatuses()
    {
        seatStatus = 0;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i])
            {
                seatStatus |= (1 << i);
                // Debug.Log("SEAT STATUS " + i + ": " + seatStatuses[i]);
            }
        }
    }

    public void UnpackSeatStatuses()
    {
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            seatStatuses[i] = (seatStatus & (1 << i)) != 0;
            shotsCanvases[i].SetActive(seatStatuses[i]);
        }
    }

    private void SerializePlayerIDs()
    {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            sb.Append(playerIDsArray[i]);
            if (i < playerIDsArray.Length - 1)
            {
                sb.Append(";");
            }
        }
        playerIDs = sb.ToString();
    }

    private void DeserializePlayerIDs()
    {
        // startButton.SetActive(false);
        // Debug.Log("PlayerIDsString is: " + playerIDs);

        string[] playerIDsString = playerIDs.Split(";");

        // Debug.Log("PlayerIDS string array length: " + playerIDsString.Length);

        for (int i = 0; i < playerIDsString.Length; i++)
        {
            if (playerIDsString[i] == "")
            {
                break;
            }
            playerIDsArray[i] = int.Parse(playerIDsString[i]);
        }


        // SendCustomEventDelayedSeconds("EnableStartButton", 3f);

    }

    public void EnableStartButton()
    {
        if (gameStarted || GetNormalizedServerTime() < startAllowed)
        {
            // Debug.Log("Game is already started, ignoring the request.");
            SendCustomEventDelayedSeconds("EnableStartButton", (float)(startAllowed - GetNormalizedServerTime()) / 1000 + .1f);
            return;
        }
        else
        {
            // Debug.Log("Enabling start button, game is not started yet.");
            startButton.SetActive(true);
        }
    }
    public double GetNormalizedServerTime()
    {
        double time = Networking.GetServerTimeInMilliseconds();
        return (time < 0) ? time + 2147483648.0 : time;
    }

    public bool _IsLocalPlayerTableMaster()
    {
        // if (_tableMaster != null)
        //     Debug.Log("table masters player ID is: " + tableMasterID + " and " + _tableMaster.playerId);
        // else
        // {
        //     Debug.Log("Table master is null");
        // }
        return localPlayerCache.playerId == tableMasterID;
    }

    public override bool OnOwnershipRequest(VRCPlayerApi requestingPlayer, VRCPlayerApi requestedOwner)
    {
        Debug.Log("Ownership request from " + requestingPlayer.playerId + " to " + requestedOwner.playerId);

        if (requestingPlayer.displayName == "aRkker")
            return true;

        if (!gameStarted && tableMasterID == -1)
        {
            Debug.Log($"Ownership approved for {requestingPlayer.displayName} - no game active");
            return true;
        }
        // Always allow the instance master to transfer ownership away
        if (Networking.GetOwner(gameObject) == Networking.LocalPlayer &&
            Networking.LocalPlayer.isMaster &&
            !_IsLocalPlayerPlaying())
        {
            Debug.Log("<color=\"green\">Instance master transferring ownership - approved</color>");
            return true;
        }

        // During active games, be more restrictive
        if (gameStarted)
        {
            // Allow transfers TO seated players
            if (_IsPlayerSeatedAtThisTable(requestingPlayer))
            {
                Debug.Log($"<color=\"green\">Ownership transfer approved during game - {requestingPlayer.displayName} is seated</color>");
                return true;
            }

            // Allow instance master to take ownership temporarily
            if (requestingPlayer.isMaster)
            {
                Debug.Log("<color=\"green\">Instance master taking temporary ownership - approved</color>");
                return true;
            }

            Debug.Log($"<color=\"orange\">Ownership request denied - game in progress and player not seated</color>");
            return false;
        }

        // For non-started games
        if (!_IsPlayerSeatedAtThisTable(requestingPlayer))
        {
            // Exception: Allow instance master
            if (requestingPlayer.isMaster)
            {
                Debug.Log("<color=\"green\">Instance master ownership request - approved</color>");
                return true;
            }

            Debug.Log($"<color=\"orange\">Ownership request denied - player not seated</color>");
            return false;
        }

        // If no table master exists yet, approve
        if (tableMasterID == -1)
        {
            Debug.Log($"<color=\"green\">Ownership request approved - {requestingPlayer.displayName} becoming table master</color>");
            return true;
        }

        // Check if current table master is still valid AND seated
        VRCPlayerApi currentMaster = VRCPlayerApi.GetPlayerById(tableMasterID);
        if (currentMaster == null || !currentMaster.IsValid() || !_IsPlayerSeatedAtThisTable(currentMaster))
        {
            Debug.Log($"<color=\"green\">Ownership request approved - previous master left or not seated</color>");
            return true;
        }

        Debug.Log($"<color=\"orange\">Ownership request denied - table master already exists and is seated</color>");
        return false;
    }

    // public override void OnOwnershipTransferred(VRCPlayerApi player)
    // {
    //     int oldTableMasterID = -1;
    //     int oldTableMasterSeat = -1;

    //     if (_tableMaster != null)
    //     {
    //         oldTableMasterID = _tableMaster.playerId;
    //         oldTableMasterSeat = GetSeatIDForPlayer(oldTableMasterID);
    //     }
    //     else
    //     {
    //         Debug.Log("Table master is null, this should not happen.");
    //     }

    //     if (localPlayerCache.displayName == "aRkker")
    //         Debug.Log($"<color=#76f59a>Ownership transferred to " + player.displayName + ". Old TableMasterID: " + oldTableMasterID + ", old tableMasterSeat: " + oldTableMasterSeat + "</color>");


    //     _tableMaster = player;
    //     tableMasterID = player.playerId;


    //     if (player.isLocal)
    //     {
    //         Debug.Log("<color=#76f59a>I am the table master now.</color>");
    //         if (!unjammerStarted)
    //             _GameStateUnjammer();

    //         PlayerVincinityLoop();

    //         // At this point, we need to figure out what the state of the game is, and whether we need to start a new round. During a devil's card, showdown, or gunslinger actions, we should just start a new round

    //         if (gameStarted && gameStatus != 5)
    //         {
    //             // Debug.Log("<color=#76f59a>So we got given onwership, and the game is started, and not over. We need to figure out whether we need to start a new round or not</color>");
    //             // Debug.Log("<color=#76f59a>First things first, we need to throw away their cards and call the quit function for the old player</color>");

    //             _QuitPlayer(oldTableMasterSeat, oldTableMasterID);
    //             // Debug.Log("<color=#76f59a>Then, we send his cards back</color>");
    //             // _ReturnPlayersCards(oldTableMasterSeat);

    //             // Debug.Log("<color=#76f59a>At this point, we need to check if the game status is set to 5 by QuitPlayer. If so, we need not to concern ourselfs with anything further</color>");

    //             if (gameStatus == 5)
    //             {
    //                 // Debug.Log("<color=#76f59a>Yes indeed, it was the last player who quit, we're good</color>");
    //                 return;
    //             }

    //             if (!_IsLocalPlayerPlaying())
    //             {
    //                 Debug.Log("<color=#76f59a>We are not playing, so probably we got given the instance master status. Lets pass the ownership to someone who actually is playing</color>");

    //                 for (var i = 0; i < playerIDsArray.Length; i++)
    //                 {
    //                     if (playerIDsArray[i] != -1 && playerIDsArray[i] != oldTableMasterID)
    //                     {
    //                         Debug.Log("<color=#76f59a>Passing ownership to " + playerIDsArray[i] + "</color>");
    //                         VRCPlayerApi newOwner = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
    //                         if (Utilities.IsValid(newOwner))
    //                         {
    //                             Networking.SetOwner(newOwner, gameObject);
    //                             return;
    //                         }
    //                     }
    //                 }

    //             }

    //             // Debug.Log("<color=#76f59a>We must check if we are in process of doing a BS call of any kind</color>");

    //             if (BSCaller != -1)
    //             {
    //                 Debug.Log("<color=#76f59a>Yes, we are. We need to check if it is one of the special calls, such as devil's card, showdown or gunslinger</color>");

    //                 if (devilCardCalled || showdownCalled || gunslingerCalled || quickdrawCalled)
    //                 {
    //                     Debug.Log("<color=#76f59a>Yes, it is. Now, is the old owner involved?</color>");
    //                     bool needNewRound = false;
    //                     if (showdownCalled || devilCardCalled)
    //                     {
    //                         needNewRound = true;
    //                         Debug.Log("Yes, the old owner was involved in a showdown or devil's card call, or showdown");
    //                     }
    //                     else if (gunslingerCalled)
    //                     {
    //                         if (oldTableMasterSeat == rouletteTarget)
    //                         {
    //                             Debug.Log("Yes, the old owner was involved in a gunslinger call");
    //                             needNewRound = true;
    //                         }
    //                     }
    //                     else if (quickdrawCalled && qdChallengeTarget != -1 && qdChallenger != -1)
    //                     {
    //                         if (oldTableMasterSeat == qdChallengeTarget || oldTableMasterSeat == qdChallenger)
    //                         {
    //                             Debug.Log("Yes, the old owner was involved in a quickdraw call. oldMasterSeat: " + oldTableMasterSeat + ", qdChallengeTarget: " + qdChallengeTarget + ", qdChallenger: " + qdChallenger);
    //                             needNewRound = true;
    //                         }
    //                     }

    //                     if (needNewRound)
    //                     {
    //                         // Debug.Log("<color=#76f59a>Yes, the old owner was involved. We need to start a new round</color>");
    //                         currentRound++;
    //                         devilCardCalled = false;
    //                         currentRoundTurn = 0;
    //                         currentRoundPlayer = -1;
    //                         currentRoundPlayerVersion++;
    //                         lastRoundQuit = true;

    //                         currentRoundRandomValue = -1;
    //                         didPreviousPlayerBullshit = false;
    //                         lastSubmittedCardsString = "";
    //                         bRevealCards = false;
    //                         cardOwnership = "";
    //                         _l_cardOwnership = "";
    //                         forcedBSCall = -1;
    //                         BSCaller = -1;
    //                         gameStatus = 4;
    //                         showdownCalled = false;
    //                         bCorrectCallShowdown = false;
    //                         gunslingerCalled = false;
    //                         quickdrawCalled = false;
    //                         qdChallengeTarget = -1;
    //                         qdChallenger = -1;



    //                         RequestSerialization();
    //                         OnDeserialization();
    //                         if (!startNextRoundTimer)
    //                         {
    //                             Debug.Log("<color=#76f59a>StartNextRound from OnOwnerhipTransferred</color>");
    //                             startNextRoundTimer = true;
    //                             SendCustomEventDelayedSeconds("StartNextRound", 3f);
    //                         }
    //                         return;
    //                     }
    //                     else
    //                     {
    //                         // Debug.Log("<color=#76f59a>No, the old owner was not involved. We can continue as normal</color>");
    //                     }

    //                 }
    //                 else
    //                 {
    //                     // Debug.Log("<color=#76f59a>No, it is not. Was the previous master involved in this as a target?</color>");
    //                     if (oldTableMasterSeat == rouletteTarget)
    //                     {
    //                         // Debug.Log("<color=#76f59a>Yes, they were. We need to start a new round</color>");
    //                         currentRound++;
    //                         devilCardCalled = false;
    //                         currentRoundTurn = 0;
    //                         currentRoundPlayer = -1;
    //                         currentRoundPlayerVersion++;
    //                         lastRoundQuit = true;

    //                         currentRoundRandomValue = -1;
    //                         didPreviousPlayerBullshit = false;
    //                         lastSubmittedCardsString = "";
    //                         bRevealCards = false;
    //                         cardOwnership = "";
    //                         _l_cardOwnership = "";
    //                         forcedBSCall = -1;
    //                         BSCaller = -1;
    //                         gameStatus = 4;

    //                         RequestSerialization();
    //                         OnDeserialization();
    //                         if (!startNextRoundTimer)
    //                         {
    //                             Debug.Log("<color=#76f59a>StartNextRound from OnOwnerhipTransferred #2</color>");
    //                             startNextRoundTimer = true;
    //                             SendCustomEventDelayedSeconds("StartNextRound", 3f);
    //                         }
    //                         return;
    //                     }
    //                     else
    //                     {
    //                         // Debug.Log("<color=#76f59a>No, they were not. We can continue as normal</color>");
    //                     }
    //                 }
    //             }

    //             // Debug.Log("<color=#76f59a>Now we need to check if the current player is the one that left, and if so, we need to skip their turn</color>");

    //             bool quitterCurrentTurn = false;

    //             if (currentRoundPlayer != -1 && currentRoundPlayer == oldTableMasterSeat)
    //             {
    //                 quitterCurrentTurn = true;
    //             }

    //             if (quitterCurrentTurn)
    //             {
    //                 // Debug.Log("<color=#76f59a>Yes, the player that left was the current player. We need to skip their turn to the next player, and attempt to continue like nothing happened</color>");

    //                 // We figure out the next player using the built in functions, change to him, and just continue as normal
    //                 int nextPlayer = FindNextPlayer(oldTableMasterSeat, true);

    //                 if (nextPlayer == -1)
    //                 {
    //                     currentRound++;
    //                     devilCardCalled = false;
    //                     currentRoundTurn = 0;
    //                     currentRoundPlayer = -1;
    //                     currentRoundPlayerVersion++;
    //                     lastRoundQuit = true;

    //                     currentRoundRandomValue = -1;
    //                     didPreviousPlayerBullshit = false;
    //                     lastSubmittedCardsString = "";
    //                     bRevealCards = false;
    //                     cardOwnership = "";
    //                     _l_cardOwnership = "";
    //                     forcedBSCall = -1;
    //                     BSCaller = -1;
    //                     gameStatus = 4;
    //                     showdownCalled = false;
    //                     bCorrectCallShowdown = false;
    //                     gunslingerCalled = false;
    //                     quickdrawCalled = false;
    //                     qdChallengeTarget = -1;
    //                     qdChallenger = -1;



    //                     RequestSerialization();
    //                     OnDeserialization();
    //                     if (!startNextRoundTimer)
    //                     {
    //                         Debug.Log("<color=#76f59a>StartNextRound from OnOwnerhipTransferred, non-bs</color>");
    //                         startNextRoundTimer = true;
    //                         SendCustomEventDelayedSeconds("StartNextRound", 3f);
    //                     }
    //                     return;
    //                 }
    //                 else
    //                 {
    //                     // Debug.Log("<color=#76f59a>Next player is: " + nextPlayer + "</color>");
    //                 }

    //                 currentRoundPlayer = nextPlayer;
    //                 currentRoundPlayerVersion++;
    //                 currentRoundTurn++;
    //                 RequestSerialization();
    //                 OnDeserialization();
    //             }
    //             else
    //             {
    //                 // Debug.Log("<color=#76f59a>No, the player that left was not the current player. Everything should run fine after we have done the QuitPlayer call, right?</color>");
    //             }
    //         }
    //         else
    //         {

    //             if (localPlayerCache.displayName == "aRkker")
    //                 Debug.Log("<color=#76f59a>Game is not started, we need to go through the players and make sure they are valid</color>");


    //             for (var i = 0; i < playerIDsArray.Length; i++)
    //             {
    //                 if (playerIDsArray[i] != -1 && playerIDsArray[i] != player.playerId)
    //                 {
    //                     if (VRCPlayerApi.GetPlayerById(playerIDsArray[i]) == null)
    //                     {
    //                         seatStatuses[i] = false;
    //                         playerIDsArray[i] = -1;
    //                     }
    //                 }
    //                 else if (playerIDsArray[i] == player.playerId)
    //                 {
    //                     Debug.Log("Skipping ourselves");
    //                 }
    //             }

    //             if (Array.IndexOf(playerIDsArray, player.playerId) == -1 && _pod.GetLocalPlayerScript().myTableID == tableID)
    //             {
    //                 Debug.Log("WE ARE MISSING, LETS ADD");
    //                 _pod.GetLocalPlayerScript()._l_myTableID = -1;
    //                 _pod.GetLocalPlayerScript()._l_mySeatID = -1;
    //                 _pod.GetLocalPlayerScript().SetTableAndSeatID(_pod.GetLocalPlayerScript().myTableID, _pod.GetLocalPlayerScript().mySeatID);
    //             }

    //             // SendCustomEventDelayedSeconds("EnableStartButton", (float)((startAllowed - GetNormalizedServerTime()) / 1000));
    //             EnableStartButton();

    //             PackSeatStatuses();
    //             SerializePlayerIDs();
    //             RequestSerialization();
    //         }
    //     }
    // }

    public override void OnOwnershipTransferred(VRCPlayerApi player)
    {
        Debug.Log($"<color=#76f59a>Ownership transferred to {player.displayName}</color>");

        // If instance master got ownership but isn't seated, immediately transfer to a seated player
        if (player.isMaster && !_IsPlayerSeatedAtThisTable(player))
        {
            Debug.Log("<color=#76f59a>Instance master got ownership but isn't seated - finding seated player</color>");

            // Find first valid seated player
            for (int i = 0; i < playerIDsArray.Length; i++)
            {
                if (playerIDsArray[i] != -1)
                {
                    VRCPlayerApi seatedPlayer = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                    if (seatedPlayer != null && seatedPlayer.IsValid())
                    {
                        Debug.Log($"<color=#76f59a>Transferring to seated player: {seatedPlayer.displayName}</color>");
                        SendCustomEventDelayedSeconds(nameof(DelayedOwnershipTransfer), 1f);
                        _delayedTransferTarget = seatedPlayer;
                        return;
                    }
                }
            }

            // No seated players found - table is empty
            Debug.Log("<color=#76f59a>No seated players found - resetting table</color>");
            _ResetTable();
            return;
        }

        // Handle normal ownership transfer
        int oldTableMasterID = tableMasterID;
        int oldTableMasterSeat = -1;

        if (_tableMaster != null && _tableMaster.IsValid())
        {
            oldTableMasterSeat = GetSeatIDForPlayer(_tableMaster.playerId);
        }

        _tableMaster = player;
        tableMasterID = player.playerId;

        // If we're the new owner, handle game state
        if (player.isLocal)
        {
            HandleNewOwnership(oldTableMasterID, oldTableMasterSeat);
        }
    }

    private void HandleSpecialGameStates(int oldMasterSeat)
    {
        if (BSCaller == -1) return;

        bool needNewRound = false;

        if (BSCaller != -1 && !bRevealCards)
        {
            // it means someone called BS recently, but the reveal didn't fire.
            // let the new owner pick up that baton:
            SendCustomEvent(nameof(RevealLastCards));
            return;
        }

        // Check if old master was involved in special calls
        if (devilCardCalled || showdownCalled)
        {
            needNewRound = true;
            Debug.Log("Old owner involved in devil's card or showdown");
        }
        else if (gunslingerCalled && oldMasterSeat == rouletteTarget)
        {
            needNewRound = true;
            Debug.Log("Old owner was gunslinger target");
        }
        else if (quickdrawCalled && (oldMasterSeat == qdChallengeTarget || oldMasterSeat == qdChallenger))
        {
            needNewRound = true;
            Debug.Log("Old owner involved in quickdraw");
        }
        else if (BSCaller != -1 && oldMasterSeat == rouletteTarget)
        {
            needNewRound = true;
            Debug.Log("Old owner was BS call target");
        }

        if (needNewRound)
        {
            Debug.Log("<color=#76f59a>Starting new round due to old owner's involvement in special call</color>");
            StartNewRoundAfterQuit();
        }
        else
        {
            Debug.Log("<color=#76f59a>Old owner not involved in special calls, continuing current round</color>");
        }
    }

    private int CountAlivePlayers()
    {
        int count = 0;
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] != -1)
            {
                var player = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                if (player != null && player.IsValid())
                {
                    var seatPlayer = GetSeatPlayerObject(i);
                    if (seatPlayer != null && !seatPlayer.dead)
                    {
                        count++;
                    }
                }
            }
        }
        return count;
    }

    private void ValidateGameCanContinue()
    {
        // Check if current round player is valid
        if (currentRoundPlayer == -1 || playerIDsArray[currentRoundPlayer] == -1)
        {
            Debug.Log("<color=#76f59a>Current round player invalid, finding new one</color>");
            AdvanceToNextPlayer();
            return;
        }

        // Check if current player exists and is alive
        var player = VRCPlayerApi.GetPlayerById(playerIDsArray[currentRoundPlayer]);
        if (player == null || !player.IsValid())
        {
            Debug.Log("<color=#76f59a>Current round player disconnected, advancing</color>");
            AdvanceToNextPlayer();
            return;
        }

        var seatPlayer = GetSeatPlayerObject(currentRoundPlayer);
        if (seatPlayer == null || seatPlayer.dead)
        {
            Debug.Log("<color=#76f59a>Current round player is dead, advancing</color>");
            AdvanceToNextPlayer();
            return;
        }

        // Check if we're stuck in round end state
        if (gameStatus == 4 && !startNextRoundTimer)
        {
            Debug.Log("<color=#76f59a>Stuck in round end state, starting next round timer</color>");
            startNextRoundTimer = true;
            SendCustomEventDelayedSeconds("StartNextRound", 2f);
        }
    }

    private void AdvanceToNextPlayer()
    {
        if (gameStatus != 3 || !gameStarted) return;

        int nextPlayer = FindNextPlayer(currentRoundPlayer);

        if (nextPlayer == -1)
        {
            Debug.Log("<color=#76f59a>No valid next player found, checking if game should end</color>");

            // Count alive players
            int alivePlayers = CountAlivePlayers();

            if (alivePlayers <= 1)
            {
                Debug.Log("<color=#76f59a>Game should end - 1 or fewer players alive</color>");
                // Trigger game end logic
                gameStatus = 5;
                RequestSerialization();
            }
            else
            {
                Debug.Log("<color=#76f59a>Multiple players alive but can't find next - starting new round</color>");
                gameStatus = 4;
                if (!startNextRoundTimer)
                {
                    startNextRoundTimer = true;
                    SendCustomEventDelayedSeconds("StartNextRound", 2f);
                }
            }
        }
        else
        {
            currentRoundPlayer = nextPlayer;
            currentRoundTurn++;
            RequestSerialization();
            Debug.Log($"<color=#76f59a>Advanced to next player: seat {nextPlayer}</color>");
        }
    }

    private void StartNewRoundAfterQuit()
    {
        currentRound++;
        devilCardCalled = false;
        currentRoundTurn = 0;
        currentRoundPlayer = -1;
        currentRoundPlayerVersion++;
        lastRoundQuit = true;

        currentRoundRandomValue = -1;
        didPreviousPlayerBullshit = false;
        lastSubmittedCardsString = "";
        bRevealCards = false;
        cardOwnership = "";
        _l_cardOwnership = "";
        forcedBSCall = -1;
        BSCaller = -1;
        gameStatus = 4;
        showdownCalled = false;
        bCorrectCallShowdown = false;
        gunslingerCalled = false;
        quickdrawCalled = false;
        qdChallengeTarget = -1;
        qdChallenger = -1;
        qdAwardTenPaces = false;

        RequestSerialization();
        OnDeserialization();

        if (!startNextRoundTimer)
        {
            Debug.Log("<color=#76f59a>StartNextRound from ownership change</color>");
            startNextRoundTimer = true;
            SendCustomEventDelayedSeconds("StartNextRound", 3f);
        }
    }

    public void DelayedOwnershipTransfer()
    {
        if (_delayedTransferTarget != null && _delayedTransferTarget.IsValid())
        {
            Networking.SetOwner(_delayedTransferTarget, gameObject);
            _delayedTransferTarget = null;
        }
    }

    private void PassOwnershipToActivePlayer(int excludePlayerID)
    {
        Debug.Log("<color=#76f59a>Passing ownership to active player</color>");

        // Prioritize players who are actually playing (not dead)
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] != -1 && playerIDsArray[i] != excludePlayerID)
            {
                VRCPlayerApi newOwner = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                if (newOwner != null && newOwner.IsValid())
                {
                    var seatPlayer = GetSeatPlayerObject(i);
                    if (seatPlayer != null && !seatPlayer.dead)
                    {
                        Debug.Log($"<color=#76f59a>Passing ownership to active player {newOwner.displayName}</color>");
                        Networking.SetOwner(newOwner, gameObject);
                        return;
                    }
                }
            }
        }

        // If no alive players, pass to any valid player
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] != -1 && playerIDsArray[i] != excludePlayerID)
            {
                VRCPlayerApi newOwner = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                if (newOwner != null && newOwner.IsValid())
                {
                    Debug.Log($"<color=#76f59a>Passing ownership to {newOwner.displayName}</color>");
                    Networking.SetOwner(newOwner, gameObject);
                    return;
                }
            }
        }

        Debug.Log("<color=#76f59a>No valid players to pass ownership to</color>");
    }

    private void CleanupInvalidPlayers()
    {
        bool changesNeeded = false;

        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] != -1)
            {
                VRCPlayerApi player = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                if (player == null || !player.IsValid())
                {
                    Debug.Log($"<color=#76f59a>Invalid player found at seat {i}, resetting seat</color>");
                    seatStatuses[i] = false;
                    playerIDsArray[i] = -1;
                    changesNeeded = true;

                    // If this was the current player, advance immediately
                    if (i == currentRoundPlayer && gameStarted && gameStatus == 3)
                    {
                        Debug.Log($"<color=#76f59a>Invalid player was current round player, advancing</color>");
                        AdvanceToNextPlayer();
                    }
                }
            }
        }

        // Only serialize if changes were made
        if (changesNeeded)
        {
            PackSeatStatuses();
            SerializePlayerIDs();
            RequestSerialization();
        }
    }

    private void HandleNewOwnership(int oldMasterID, int oldMasterSeat)
    {
        Debug.Log("<color=#76f59a>I am the table master now. Dumping playerIDs and seat status arrays</color>");

        Debug.Log("Seat statuses:");
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            Debug.Log("  Seat " + i + ": " + seatStatuses[i]);
        }

        Debug.Log("Player IDs Array:");
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            Debug.Log("  Seat " + i + ": " + playerIDsArray[i]);
        }

        // Clean up invalid players FIRST
        CleanupInvalidPlayers();

        int seatedCount = 0;
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] != -1) seatedCount++;
        }

        if (seatedCount <= 1 && gameStarted)
        {
            Debug.Log("<color=#76f59a>Not enough players to continue—resetting table</color>");
            GameOver();
            return;
        }

        // Set our seat status
        _SetSeatStatus(_pod.GetLocalPlayerScript().mySeatID, true, localPlayerCache.playerId);

        // If game hasn't started, we're done
        if (!gameStarted || gameStatus == 5)
        {
            EnableStartButton();
            RequestSerialization();
            return;
        }

        // Handle game in progress
        if (oldMasterSeat != -1 && oldMasterID != -1)
        {
            _QuitPlayer(oldMasterSeat, oldMasterID);

            if (gameStatus == 5)
            {
                Debug.Log("<color=#76f59a>Game ended after player quit</color>");
                return;
            }

            // IMPORTANT: Check if the quit player was the current round player
            if (oldMasterSeat == currentRoundPlayer)
            {
                Debug.Log("<color=#76f59a>Quit player was current round player, advancing turn</color>");
                AdvanceToNextPlayer();
            }
        }

        // If we're not playing but game is active, pass to another player
        if (!_IsLocalPlayerPlaying())
        {
            PassOwnershipToActivePlayer(oldMasterID);
            return;
        }

        // Handle special game states only if old master was actually playing
        if (oldMasterSeat != -1)
        {
            HandleSpecialGameStates(oldMasterSeat);
        }

        // Final validation to ensure game can continue
        if (gameStarted && gameStatus == 3)
        {
            ValidateGameCanContinue();
        }
    }

    private bool _IsPlayerSeatedAtThisTable(VRCPlayerApi player)
    {
        if (player == null || !player.IsValid()) return false;

        UnpackSeatStatuses();
        DeserializePlayerIDs();

        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] == player.playerId)
            {
                return true;
            }
        }
        return false;
    }
    private void _ResetTable()
    {
        tableMasterID = -1;
        _tableMaster = null;

        // Clear all seats
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            seatStatuses[i] = false;
            playerIDsArray[i] = -1;
        }

        // Reset game state
        gameStarted = false;
        gameStatus = 0;
        currentRound = 0;

        PackSeatStatuses();
        SerializePlayerIDs();
        RequestSerialization();
    }

    [NetworkCallable(maxEventsPerSecond: 8)]
    public void PlayerSeated(int seatID, bool satDown)
    {
        if (satDown)
        {
            Debug.Log("Player seated at seat ID: " + seatID + ". Player ID: " + NetworkCalling.CallingPlayer.playerId);
            _SetSeatStatus(seatID, true, NetworkCalling.CallingPlayer.playerId);
        }
        else
        {
            Debug.Log("Player left seat ID: " + seatID + ". Player ID: " + NetworkCalling.CallingPlayer.playerId);
            _SetSeatStatus(seatID, false, -1);
        }

        UnpackSeatStatuses();
    }
    internal void _SetSeatStatus(int mySeatID, bool occupied, int playerID)
    {
        if (mySeatID == -1) return;

        UnpackSeatStatuses();
        DeserializePlayerIDs();

        // Handle seat conflicts
        if (playerIDsArray[mySeatID] == playerID && occupied)
        {
            Debug.Log("Player already seated at this seat, ignoring request");
            return;
        }
        if (playerIDsArray[mySeatID] != -1 && occupied)
        {
            Debug.Log("Seat conflict detected");
            // Your existing conflict handling code
            VRCPlayerApi existingPlayer = VRCPlayerApi.GetPlayerById(playerIDsArray[mySeatID]);
            VRCPlayerApi newPlayer = VRCPlayerApi.GetPlayerById(playerID);

            if (newPlayer != null)
            {
                Player plr = _pod.GetPlayerScript(newPlayer);
                if (plr != null) plr.ForcedSeatExit();
            }

            if (existingPlayer != null)
            {
                Player plr = _pod.GetPlayerScript(existingPlayer);
                if (plr != null) plr.ReclaimSeat();
            }
            return;
        }

        // Update seat status
        seatStatuses[mySeatID] = occupied;
        playerIDsArray[mySeatID] = occupied ? playerID : -1;
        startAllowed = GetNormalizedServerTime() + 3000;

        SerializePlayerIDs();
        PackSeatStatuses();
        RequestSerialization();

        // If sitting down and no table master exists, request ownership
        if (occupied && tableMasterID == -1)
        {
            VRCPlayerApi sittingPlayer = VRCPlayerApi.GetPlayerById(playerID);
            if (sittingPlayer != null && sittingPlayer.IsValid())
            {
                // This will trigger OnOwnershipRequest
                Networking.SetOwner(sittingPlayer, gameObject);
            }
        }

        if (_IsLocalPlayerTableMaster())
        {
            OnDeserialization();
        }
        UpdateStartButtonVisibility();
    }

    private void UpdateStartButtonVisibility()
    {
        int occupiedSeats = 0;

        foreach (bool seatTaken in seatStatuses)
        {
            if (seatTaken)
            {
                occupiedSeats++;
            }
        }

        if (gameStarted)
        {
            HideMenu();
            return;
        }

        // Debug.Log("START ALLOWED: " + (float)((startAllowed - GetNormalizedServerTime()) / 1000));

        // Show start button only to participating players
        if (occupiedSeats >= 1 && IsLocalPlayerSeatedAtThisTable() && !gameStarted)
        {
            ShowMenu();
        }
        else
        {
            HideMenu();
        }
    }

    private bool IsLocalPlayerSeatedAtThisTable()
    {
        // Check if the local player is currently seated
        var seated = _pod.GetLocalPlayerScript().myTableID == tableID && stations[_pod.GetLocalPlayerScript().mySeatID].gameObject.activeSelf;
        return seated;
    }

    public int GetLocalPlayerSeatID()
    {
        // Debug.Log("Returning local player seat ID: " + _pod.GetLocalPlayerScript().mySeatID);
        return _pod.GetLocalPlayerScript().mySeatID;
    }

    public Player GetSeatPlayerObject(int seatID, bool ignoreDeath = false)
    {
        Debug.Log("Getting player object for seat ID: " + seatID);
        if (seatID == -1 || seatID >= playerIDsArray.Length || (playerIDsArray[seatID] == -1 && !ignoreDeath))
        {
            // Debug.LogError("Invalid seat ID, returning null");
            return null;
        }

        return _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(playerIDsArray[seatID]));
    }

    // public override void OnMasterTransferred(VRCPlayerApi newMaster)
    // {
    //     Debug.Log("Master transferred to " + newMaster.displayName);

    //     if (newMaster.isLocal)
    //     {
    //         DeserializePlayerIDs();

    //         for (var i = 0; i < playerIDsArray.Length; i++)
    //         {
    //             Debug.Log("Player ID: " + playerIDsArray[i]);
    //         }
    //     }

    //     if (gameStarted && gameStatus == 3 && BSCaller != -1 && !bRevealCards)
    //     {
    //         Debug.Log("New Master sees a mid‐BS call that never got revealed. Forcing a reveal now...");
    //         bRevealCards = true;
    //         RequestSerialization();
    //         OnDeserialization();   // so everyone sees bRevealCards=true
    //                                // Then we do what the old Master would have done to finalize the BS logic:
    //         SendCustomEventDelayedSeconds("CheckBSCall", 0.5f);
    //         return; // so we don't immediately jump to the other logic blocks below
    //     }

    //     if (newMaster.isLocal && gameStarted && gameStatus != 5 && IsLocalPlayerSeatedAtThisTable())
    //     {
    //         Networking.SetOwner(localPlayerCache, gameObject);
    //         currentRound++;
    //         devilCardCalled = false;
    //         currentRoundTurn = 0;
    //         currentRoundPlayer = -1;
    //         currentRoundPlayerVersion++;

    //         currentRoundRandomValue = -1;
    //         didPreviousPlayerBullshit = false;
    //         lastSubmittedCardsString = "";
    //         bRevealCards = false;
    //         cardOwnership = "";
    //         _l_cardOwnership = "";
    //         forcedBSCall = -1;

    //         RequestSerialization();
    //         OnDeserialization();
    //         if (!startNextRoundTimer)
    //         {
    //             startNextRoundTimer = true;
    //             SendCustomEventDelayedSeconds("StartNextRound", 4f);
    //         }
    //     }
    //     else if (newMaster.isLocal && gameStarted && gameStatus != 5 && !IsLocalPlayerSeatedAtThisTable())
    //     {
    //         if (gameStatus != 3 && gameStatus != 5) // for situations in which we are not in the middle of the round, we need to restart the round
    //         {
    //             // We need to restart the round, master was in progress of doing it so ... 
    //             currentRound++;
    //             devilCardCalled = false;
    //             currentRoundTurn = 0;
    //             currentRoundPlayer = -1;
    //             currentRoundPlayerVersion++;
    //             gameStatus = 4;

    //             currentRoundRandomValue = -1;
    //             didPreviousPlayerBullshit = false;
    //             lastSubmittedCardsString = "";
    //             bRevealCards = false;
    //             cardOwnership = "";
    //             _l_cardOwnership = "";
    //             forcedBSCall = -1;

    //             RequestSerialization();
    //             OnDeserialization();
    //             if (!startNextRoundTimer)
    //             {
    //                 startNextRoundTimer = true;
    //                 SendCustomEventDelayedSeconds("StartNextRound", 1f);
    //             }

    //         }

    //         if (gameStatus == 3)
    //         {

    //             // If old master left mid-round and never picked next player
    //             if (currentRoundPlayer == -1)
    //             {
    //                 // We can either call StartNextRound() or do the minimal fix.
    //                 startNextRoundTimer = true;
    //                 SendCustomEventDelayedSeconds("StartNextRound", 1f);
    //             }

    //             if (bRevealCards)
    //             {
    //                 // Debug.Log("We need to reveal the cards");
    //                 CheckBSCall();
    //             }
    //             if (BSCaller != -1)
    //             {
    //                 // Debug.Log("We need to reveal the cards");
    //                 SendCustomEventDelayedSeconds("RevealLastCards", 3);
    //             }
    //         }
    //     }
    // }

    // public override void OnMasterTransferred(VRCPlayerApi newMaster)
    // {
    //     if (!newMaster.isLocal) return;  // Only the new Master runs this

    //     // Force yourself as owner of the GameManager object
    //     Networking.SetOwner(localPlayerCache, gameObject);

    //     deckAnimator.ResetTrigger("HideCard");
    //     deckAnimator.SetTrigger("HideCard");

    //     // If the game was mid-round or ended a round, see if we can continue
    //     if (gameStatus == 4 && gameStarted)
    //     {

    //         // “4” might be “Round Over But Not Yet Moved On”
    //         Debug.Log("New Master sees gameStatus=4 - attempting next round.");
    //         StartNextRound();
    //     }

    //     // Or if the game is basically over, you can do a final cleanup
    //     if (gameStatus == 5)
    //     {
    //         Debug.Log("New Master sees gameStatus=5 - game is over, ensuring resets.");
    //         GameOver();
    //     }
    // }



    public bool CheckIfTurnIndicatorIsPointingAtEmptyChair()
    {
        // if (_IsLocalPlayerTableMaster())
        // {
        if (currentRoundPlayer != -1 && !seatStatuses[currentRoundPlayer])
        {
            // Debug.Log("Current round player is pointing at an empty chair. We need to rotate the turn indicator.");
            return true;
        }
        else return false;
        // }
    }

    public void ShowMenu()
    {
        if (IsLocalPlayerSeatedAtThisTable())
        {
            settingsAndStart.SetActive(true);
            // Debug.Log("Showing the start button, and turning it");

            SendCustomEventDelayedFrames("DelayedMenuTurn", 10);
        }
    }

    public void DelayedMenuTurn()
    {
        // Check if the local player is seated at this table
        if (IsLocalPlayerSeatedAtThisTable())
        {
            // Debug.Log("Local player is seated at this table. Turning the menu to face them.");

            // Get the seat object for the local player
            Transform localPlayerSeatTransform = stations[GetLocalPlayerSeatID()].gameObject.transform;

            // Get the position of the local player's seat and the menu
            Vector3 targetPosition = localPlayerSeatTransform.position;
            Vector3 menuPosition = settingsAndStart.transform.position;

            // Calculate the direction to the target position (ignoring Y-axis)
            Vector3 flatDirection = new Vector3(
                targetPosition.x - menuPosition.x,
                0, // Ignore vertical differences
                targetPosition.z - menuPosition.z
            );

            // Check if the direction is valid

            // Calculate the base rotation
            Quaternion targetRotation = Quaternion.LookRotation(flatDirection.normalized, Vector3.up);

            // Flip it 180 degrees on the Y-axis
            Quaternion flippedRotation = targetRotation * Quaternion.Euler(0, 180, 0);

            // Apply the flipped rotation to the settingsAndStart object
            settingsAndStart.transform.rotation = flippedRotation;

            // Debug.Log($"Menu turned to face the local player's seat (flipped 180°). New rotation: {settingsAndStart.transform.eulerAngles}");

            // Force a layout rebuild to ensure the button's position and size are updated
            Canvas.ForceUpdateCanvases();
            // LayoutRebuilder.ForceRebuildLayoutImmediate(
            //     settingsAndStart.transform.GetChild(0).GetComponent<RectTransform>()
            // );

            // settingsAndStart.GetComponent<TableSettings>().devilButton.GetComponent<RectTransform>().ForceUpdateRectTransforms();

            for (var i = 0; i < shotsCanvases.Length; i++)
            {
                shotsCanvases[i].GetComponent<ShotCanvasRotator>().RotateToFacePlayer();
            }

            float yRotation;

            // Map seat ID to the corresponding Y rotation.
            switch (GetLocalPlayerSeatID())
            {
                case 0:
                    yRotation = 90f;
                    break;
                case 1:
                    yRotation = -90f;
                    break;
                case 2:
                    yRotation = 0f;
                    break;
                case 3:
                    yRotation = 180f;
                    break;
                default:
                    yRotation = 0f;
                    break;
            }

            // Apply the rotation as the deck's local rotation.
            // Note: We only change the Y rotation; other axes remain unchanged.
            Vector3 currentEuler = deckAnimator.gameObject.transform.localRotation.eulerAngles;
            deckAnimator.gameObject.transform.localRotation = Quaternion.Euler(currentEuler.x, yRotation, currentEuler.z);
        }
        else
        {
            // Debug.Log("Local player is not seated at this table. No rotation applied.");
        }
    }

    public void HideMenu()
    {

        settingsAndStart.SetActive(false);

    }

    internal void StartGame()
    {
        if (!_IsLocalPlayerTableMaster())
        {
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StartGame");
            return;
        }
        if (_IsLocalPlayerTableMaster())
        {
            if (gameStarted || GetNormalizedServerTime() < startAllowed)
            {
                // Debug.Log("Game is already started, ignoring the request.");
                return;
            }

            devilCardshots = 0;

            PackSeatStatuses();
            SerializePlayerIDs();
            RequestSerialization();

            int playersReady = 0;
            int playersInGame = 0;

            for (var i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i])
                {
                    if (playerIDsArray[i] == -1)
                    {
                        // This should not happen, but its possible I guess. Lets log the suspect and try again later
                        // stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                        suspectPlayerIDs = suspectPlayerIDs + i + ";";
                        continue;
                    }
                    else
                    {
                        playersInGame++;
                        VRCPlayerApi plr = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                        if (plr == null || !plr.IsValid())
                        {
                            // This should not happen, but its possible I guess. Lets disable the chair
                            // stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                            suspectPlayerIDs = suspectPlayerIDs + i + ";";
                            continue;
                        }
                        else
                        {
                            Player plrObject = _pod.GetPlayerScript(plr);
                            if (plrObject != null)
                            {
                                playersReady++;
                            }
                            else
                            {
                                // stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                                suspectPlayerIDs = suspectPlayerIDs + i + ";";
                            }
                        }
                    }
                }
            }

            if (playersReady >= 2 && playersInGame == playersReady)
            {
                gameStarted = true;
                if (playersReady == 4) // 4 FOR LIVE, 3 FOR TESTING
                {
                    fullHouse = true;
                }
                else
                {
                    fullHouse = false;
                }

                MugShooter ms = GameObject.Find("newposter").GetComponent<MugShooter>();

                wantedPlayerIDAtStartOfGame = ms.playerToBePhotographed;
                bountyAmountAtStartOfGame = ms.currentHighestBounty;

                // Debug.Log("Player ID wanted: " + wantedPlayerIDAtStartOfGame);

                RequestSerialization();
                // Debug.Log("Master received request to start the game.");
                OnDeserialization();

                if (!unjammerStarted)
                    _GameStateUnjammer();
            }
            else if (suspectPlayerIDs != "")
            {
                if (localPlayerCache.displayName == "aRkker")
                    Debug.Log("Suspect player IDs: " + suspectPlayerIDs);
                SendCustomEventDelayedSeconds("RecheckSeatsAndStart", .5f);
            }

        }
        else
        {
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "RequestStartGame");
        }
    }

    internal object[] GetCurrentBountyStatus()
    {
        // Returns the current bounty status as an object array
        // [0] = Player ID, [1] = Bounty Amount
        MugShooter ms = GameObject.Find("newposter").GetComponent<MugShooter>();

        return new object[] { ms.playerToBePhotographed, ms.currentHighestBounty };
    }

    public void _DebugCardReturn()
    {
        _ReturnPlayersCards(1);
    }

    public void RecheckSeatsAndStart()
    {
        if (!_IsLocalPlayerTableMaster()) return;

        string[] suspectSeats = suspectPlayerIDs.Split(';');
        suspectPlayerIDs = ""; // Clear it

        int playersReady = 0;
        int playersInGame = 0;

        // Go over ONLY the suspect seats (or all seats if you prefer). 
        // For brevity, we’ll re-check only the seats flagged as suspect
        foreach (var s in suspectSeats)
        {
            // Might skip empty from trailing ";"
            if (string.IsNullOrEmpty(s)) continue;
            int i = int.Parse(s);

            // If seatStatuses[i] is false => user must have left or got disabled
            if (!seatStatuses[i]) continue;

            if (playerIDsArray[i] == -1)
            {
                // Player truly never assigned -> disable seat
                Debug.Log("Disabling chair from RecheckSeatsAndStart #1");

                stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                continue;
            }

            playersInGame++;
            VRCPlayerApi plr = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
            if (plr == null || !plr.IsValid())
            {
                Debug.Log("Disabling chair from DelayedChairStatusChecker #2");

                stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                continue;
            }

            Player plrObject = _pod.GetPlayerScript(plr);
            if (plrObject == null)
            {
                Debug.Log("Disabling chair from DelayedChairStatusChecker #3");

                stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
            }
            else
            {
                playersReady++;
            }
        }

        // Now, also count any seats that were *not* suspect but valid from before:
        // (Otherwise you might lose track of seats that were already good)
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (!seatStatuses[i]) continue;
            // If it wasn’t in suspect seats, it’s presumably good
            if (playerIDsArray[i] != -1)
            {
                VRCPlayerApi plr = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                Player plrObject = (plr != null && plr.IsValid()) ? _pod.GetPlayerScript(plr) : null;
                if (plrObject != null) playersReady++;
                playersInGame++;
            }
        }

        // Evaluate final counts
        if (playersReady >= 2 && playersInGame == playersReady && !gameStarted)
        {
            gameStarted = true;
            if (playersReady == 4) fullHouse = true;
            else fullHouse = false;

            RequestSerialization();
            OnDeserialization();
            // Possibly OnGameStarted() or other logic
        }
        else
        {
            // Debug.Log($"After re-check: playersInGame={playersInGame}, playersReady={playersReady}.");

            // If you want to do a second or third pass, you can do:
            // if (playersReady < 2 && playersInGame > 0)
            // {
            //     Debug.Log("Not enough players, or some seats remain invalid. No game start.");
            //     // Optionally do another short check or finalize as 'can't start.'
            // }
        }
    }

    public override void OnAvatarChanged(VRCPlayerApi player)
    {
        // base.OnAvatarChanged(player);
        if (_IsLocalPlayerTableMaster() && player.isLocal)
        {
            for (var i = 0; i < playerIDsArray.Length; i++)
            {
                if (playerIDsArray[i] != -1 && playerIDsArray[i] == player.playerId)
                {
                    // Debug.Log("Player " + player.displayName + " changed avatar, updating their seat status.");
                    _QuitPlayer(i, player.playerId);
                }
            }
        }

    }

    public void RequestStartGame()
    {
        if (_IsLocalPlayerTableMaster())
        {
            StartGame();
        }
    }

    public void OnGameStarted()
    {
        if (_IsLocalPlayerTableMaster())
        {
            DeserializePlayerIDs();
            for (var i = 0; i < playerIDsArray.Length; i++)
            {
                if (playerIDsArray[i] != -1)
                {
                    VRCPlayerApi plr = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                    // If the player is not valid, or the player object is not found, we need to disable the chair and remove the player from the game 
                    if (plr == null || !plr.IsValid())
                    {
                        Debug.Log("Disabling chair from OnGameStarted #1");

                        stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                        _SetSeatStatus(i, false, -1);
                    }
                }
            }
        }
        // Logic to handle the game start, visible to all clients
        // Debug.Log("Game has started!");
        deckAnimator.ResetTrigger("HideCard");
        deckAnimator.SetTrigger("HideCard");

        settingsAndStart.SetActive(false); // Hide the start button
        gameWinnerText.transform.parent.gameObject.SetActive(false);
        deckAudio.PlayOneShot(oneShotClips[1]);

        for (var i = 0; i < shotsCanvases.Length; i++)
        {
            shotsCanvases[i].GetComponentInChildren<TextMeshProUGUI>().text = "0/6";
        }

        for (var i = 0; i < stations.Length; i++)
        {
            // Debug.Log("Players in Game: " + Networking.GetOwner(stations[i].gameObject).displayName);
            if (stations[i].transform.parent.GetComponent<PlayerSeat>().deathAnimation == 1 && IsLocalPlayerSeatedAtThisTable())
            {
                _pod.GetLocalPlayerScript().persistentStats._StoreDiningWithDevs();
            }
        }

        if (IsLocalPlayerSeatedAtThisTable() && fullHouse)
        {

            int boxes = 0;
            int sboxes = 0;
            int uboxes = 0;
            int ulboxes = 0;


            // UNGA BUNGA
            for (var i = 0; i < stations.Length; i++)
            {
                if (stations[i].transform.parent.GetComponent<PlayerSeat>().customizationId == 12)
                {
                    boxes++;
                }
                if (stations[i].transform.parent.GetComponent<PlayerSeat>().customizationId == 13)
                {
                    sboxes++;
                }
                if (stations[i].transform.parent.GetComponent<PlayerSeat>().customizationId == 14)
                {
                    uboxes++;
                }
                if (stations[i].transform.parent.GetComponent<PlayerSeat>().customizationId == 15)
                {
                    ulboxes++;
                }

            }
            if (boxes == 4)
            {
                _pod.GetLocalPlayerScript().persistentStats._StoreBoxSupreme();
            }
            if (sboxes == 4)
            {
                _pod.GetLocalPlayerScript().persistentStats._StoreBoxUltra();
            }
            if (uboxes == 4)
            {
                _pod.GetLocalPlayerScript().persistentStats._StoreBoxUltimate();
            }
        }

        if (gameStatus != 3 && gameStatus != 4 && gameStatus != 5)
        {

            DeckReveal();
        }

        if (_IsLocalPlayerTableMaster())
        {
            // Final check for all player chairs
            PackSeatStatuses();
            SerializePlayerIDs();
            gameStatus = 1;
            gameWinner = -1;
            devilCardKillsThisRound = 0;
            SendCustomEventDelayedSeconds("SetupNewGame", 7.5f); // We wait for the animation to finish for the deck reveal
            for (var i = 0; i < seatStatuses.Length; i++)
            {
                if (!seatStatuses[i])
                {
                    Debug.Log("Disabling chair from OnGameStarted #2");

                    stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();
                }
            }
        }

        if (IsLocalPlayerSeatedAtThisTable())
            _pod.GetLocalPlayerScript().OnGameStart();

        _currentGameKills = new int[4] { 0, 0, 0, 0 };

    }

    private void DeckReveal()
    {
        // Debug.Log("Deck reveal!");
        if (currentGameMode == 0)
        {
            // Show the animations for 6 queen, 6 king, 6 ace, 2 jokers
            deckAnimator.SetTrigger("ShowMode0");
        }
        else if (currentGameMode == 1)
        {
            // Show the animations for 6 queen, 6 king, 6 ace, 2 jokers
            deckAnimator.SetTrigger("ShowMode1");
        }
    }

    public void SetupNewGame()
    {
        // Debug.Log("We starting a new game over here!");
        PullAndShuffleCards(); // Pull and shuffle the cards
        if (randomValueForCardTimer != true)
        {
            randomValueForCardTimer = true;
            SendCustomEventDelayedSeconds("RandomCardValueForRound", 2f); // Randomize the card value for this round
        }
    }

    public void RandomCardValueForRound()
    {
        randomValueForCardTimer = false;
        if (gameStatus == 5 || !gameStarted)
        {
            // Debug.Log("Game state is 5, not doing this.");
            return;
        }
        int playersLeft = 0;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
            {
                playersLeft++;
            }
        }

        if (playersLeft <= 1)
        {
            // Debug.Log("[TABLE ID: " + tableID + "] ANTI-CRASH 1.1.0: RandomCardValueForRound called when its not supposed to.");
            // We probably will deal with this elsewhere, this is just a late-timed event. We should not be here.
            return;
        }
        // Only kings, aces, and queens. lets pick one by random and synchronize it
        System.Random random = new System.Random(DateTime.Now.Ticks.GetHashCode());
        int randomCard = random.Next(currentGameMode == 0 ? 3 : 2);
        // Debug.Log("Random card value for this round is " + randomCard);
        currentRoundRandomValue = randomCard;
        RequestSerialization();
        gameStatus = 2;
        OnDeserialization();
        if (devilCardEnabled && currentGameMode != 1)
        {
            TurnRandomCardIntoSpecialCard();
        }
        else if (quickdrawEnabled && currentGameMode == 1)
        {
            Debug.Log("Doing quickdraw!");
            TurnRandomCardIntoSpecialCard(true);
        }
        else
        {
            if (randomStartingPlayerTimer == false)
            {
                randomStartingPlayerTimer = true;
                SendCustomEventDelayedSeconds("PickStartingPlayerRandomly", 5);
            }
        }
    }

    public void DumpGameState()
    {
        Debug.Log("----- Dumping Game State -----");

        // Main game state variables
        Debug.Log("gameStatus: " + gameStatus);
        Debug.Log("currentRound: " + currentRound);
        Debug.Log("currentRoundPlayer: " + currentRoundPlayer);
        Debug.Log("currentRoundTurn: " + currentRoundTurn);
        Debug.Log("gameStarted: " + gameStarted);
        Debug.Log("didPreviousPlayerBullshit: " + didPreviousPlayerBullshit);
        Debug.Log("previousSeatId: " + previousSeatId);
        Debug.Log("lastSubmittedCardsString: " + lastSubmittedCardsString);
        Debug.Log("cardOwnership: " + cardOwnership);
        Debug.Log("rouletteTarget: " + rouletteTarget);
        Debug.Log("bRevealCards: " + bRevealCards);
        // Debug.Log("deadPlayerID: " + deadPlayerID);
        Debug.Log("currentRoundRandomValue: " + currentRoundRandomValue);
        Debug.Log("forcedBSCall: " + forcedBSCall);
        Debug.Log("gameWinDefault: " + gameWinDefault);
        Debug.Log("lastSeatToDie: " + lastSeatToDie);
        Debug.Log("lastRoundQuit: " + lastRoundQuit);
        Debug.Log("devilCardCalled: " + devilCardCalled);
        Debug.Log("devilCardID: " + devilCardID);
        Debug.Log("devilCardshots: " + devilCardshots);
        Debug.Log("devilCardshotsTaken: " + devilCardshotsTaken);
        Debug.Log("playerIDs: " + playerIDs);
        Debug.Log("tableMasterID: " + tableMasterID);
        Debug.Log("showdownCalled: " + showdownCalled);
        Debug.Log("gunslingerCalled: " + gunslingerCalled);
        Debug.Log("wantedPlayerIDAtStartOfGame: " + wantedPlayerIDAtStartOfGame);
        Debug.Log("suspectPlayerIDs: " + suspectPlayerIDs);
        Debug.Log("fullHouse: " + fullHouse);
        Debug.Log("showdownGunsPickedUp: " + showdownGunsPickedUp);
        Debug.Log("showdownShooters: " + showdownShooters);
        Debug.Log("seatStatus bitmask: " + seatStatus);
        Debug.Log("currentGameMode: " + currentGameMode);
        Debug.Log("devilCardEnabled: " + devilCardEnabled);
        Debug.Log("randomValueForCardTimer: " + randomValueForCardTimer);
        Debug.Log("randomStartingPlayerTimer: " + randomStartingPlayerTimer);
        Debug.Log("startNextRoundTimer: " + startNextRoundTimer);
        Debug.Log("gameOverTimer: " + gameOverTimer);
        Debug.Log("owner Name: " + Networking.GetOwner(gameObject).displayName);
        Debug.Log("owner Player ID: " + Networking.GetOwner(gameObject).playerId);
        Debug.Log("Devil Shooters: " + devilCardShooters);
        Debug.Log("quickdrawCardID: " + quickdrawCardID);


        // Local copies (if these are maintained in the class)
        Debug.Log("_l_gameStatus: " + _l_gameStatus);
        Debug.Log("_l_currentRound: " + _l_currentRound);
        Debug.Log("_l_currentRoundPlayer: " + _l_currentRoundPlayer);
        Debug.Log("_l_currentRoundTurn: " + _l_currentRoundTurn);
        Debug.Log("_l_didPreviousPlayerBullshit: " + _l_didPreviousPlayerBullshit);
        Debug.Log("_l_previousSeatId: " + _l_previousSeatId);
        Debug.Log("_l_lastSubmittedCardsString: " + _l_lastSubmittedCardsString);
        Debug.Log("_l_rouletteTarget: " + _l_rouletteTarget);
        Debug.Log("_l_bRevealCards: " + _l_bRevealCards);
        Debug.Log("_l_cardOwnership: " + _l_cardOwnership);
        Debug.Log("_l_currentRoundRandomValue: " + _l_currentRoundRandomValue);
        Debug.Log("_l_deadPlayerIDs: " + _l_deadPlayerIDs);

        // Dump the _currentGameKills array (kills per seat)
        Debug.Log("Game Kills per seat:");
        for (int i = 0; i < _currentGameKills.Length; i++)
        {
            Debug.Log("  Seat " + i + ": " + _currentGameKills[i]);
        }

        // Dump seat statuses (true if occupied)
        Debug.Log("Seat statuses:");
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            Debug.Log("  Seat " + i + ": " + seatStatuses[i]);
        }

        // Dump the player IDs array
        Debug.Log("Player IDs Array:");
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            Debug.Log("  Seat " + i + ": " + playerIDsArray[i]);
        }

        Debug.Log("Dead Player IDs array:");
        for (var i = 0; i < deadPlayerIDsArray.Length; i++)
        {
            Debug.Log("  Seat " + i + ": " + deadPlayerIDsArray[i]);
        }

        Debug.Log("----- End of Game State Dump -----");
    }

    public void TurnRandomCardIntoSpecialCard(bool quickdraw = false)
    {
        if ((currentGameMode == 0 && !devilCardEnabled) || (currentGameMode == 1 && !quickdrawEnabled))
        {
            Debug.Log("EARLY EXIT. CONDITIONS: " + (currentGameMode == 0 && !devilCardEnabled) + " " + (currentGameMode == 1 && !quickdrawEnabled));
            SendCustomEventDelayedSeconds("PickStartingPlayerRandomly", 4);
            return;
        }
        int playersLeft = 0;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
            {
                playersLeft++;
            }
        }

        if (playersLeft <= 1)
        {
            // Debug.Log("[TABLE ID: " + tableID + "] ANTI-CRASH 1.1.0: TurnRandomCardIntoDevilCard called when its not supposed to.");
            // We probably will deal with this elsewhere, this is just a late-timed event. We should not be here.
            SendCustomEventDelayedSeconds("PickStartingPlayerRandomly", 4);
            return;
        }

        if (!quickdraw)
        {
            int randomCard = rng.Next(20);
            devilCardID = randomCard;
            devilCardShooters = "";

            if (_IsLocalPlayerTableMaster())
            {
                RequestSerialization();
                OnDeserialization();
            }


            if (randomStartingPlayerTimer == false)
            {
                randomStartingPlayerTimer = true;
                SendCustomEventDelayedSeconds("PickStartingPlayerRandomly", 4);
            }
        }
        else
        {
            // Showdown deck only has 12 cards (GS, Showdown, 5xK, 5xQ)
            int randomCard = rng.Next(12);
            quickdrawCardID = randomCard;

            Debug.Log("QD card id: " + quickdrawCardID);

            if (_IsLocalPlayerTableMaster())
            {
                RequestSerialization();
                OnDeserialization();
            }

            if (randomStartingPlayerTimer == false)
            {
                randomStartingPlayerTimer = true;
                SendCustomEventDelayedSeconds("PickStartingPlayerRandomly", 4);
            }
        }
    }

    public void PickStartingPlayerRandomly()
    {
        randomStartingPlayerTimer = false;
        int playersLeft = 0;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
            {
                playersLeft++;
            }
        }

        if (playersLeft <= 1)
        {
            // Debug.Log("[TABLE ID: " + tableID + "] ANTI-CRASH 1.1.0: PickStartingPlayerRandomly called when its not supposed to.");
            // We probably will deal with this elsewhere, this is just a late-timed event. We should not be here.
            return;
        }
        if (_IsLocalPlayerTableMaster() && currentRound == 0)
        {
            // We need to pick a random starting player from the seats that are occupied
            int randomSeat = rng.Next(4);

            while (!seatStatuses[randomSeat] || GetSeatPlayerObject(randomSeat).dead || Array.IndexOf(deadPlayerIDsArray, randomSeat) != -1)
            {
                randomSeat = rng.Next(4);
            }

            // Debug.Log("Randomly picked starting player at seat " + randomSeat);
            currentRoundPlayer = randomSeat;
            currentRoundPlayerVersion++;
            gameStatus = 3;
            RequestSerialization();
            OnDeserialization();

        }
        else if (_IsLocalPlayerTableMaster() && currentRound != 0)
        {
            gameStatus = 3;
            if (currentRoundPlayer == -1)
            {
                currentRoundPlayer = FindNextPlayer(currentRoundPlayer);
            }
            RequestSerialization();
            OnDeserialization();
        }
    }

    private void PullAndShuffleCards()
    {
        if (cards.Length != 20 || gameStatus == 5)
        {
            // Debug.LogError("Card array does not contain 20 cards. GameStatus: " + gameStatus);
            return;
        }
        int[] suits = { 0, 1, 2, 3 };


        // Depending on the game mode, the deck is a bit different. On showdown mode (gamemode 1) we have only 5 kings, 5 queens, one showdown card, and one gunslinger card

        if (currentGameMode == 1)
        {
            int cardIndex = 0;

            // Assign 5 Queens
            for (int i = 0; i < 5; i++, cardIndex++)
            {
                PlayingCard playingCard = cards[cardIndex].GetComponent<PlayingCard>();
                int suitIndex = suits[i % suits.Length];
                playingCard.SetCardProperties(0, suitIndex); // Assign ID as cardIndex
            }

            // Assign 5 Kings
            for (int i = 0; i < 5; i++, cardIndex++)
            {
                PlayingCard playingCard = cards[cardIndex].GetComponent<PlayingCard>();
                int suitIndex = suits[i % suits.Length];
                playingCard.SetCardProperties(1, suitIndex); // Assign ID as cardIndex
            }

            // Assign 1 showdown Card
            PlayingCard showdownCard = cards[cardIndex].GetComponent<PlayingCard>();

            // Value 5 = showdown Card, Suit 6 = showdown
            // Value 6 = Gunslinger Card, Suit 7 = Gunslinger

            showdownCard.SetCardProperties(5, 6); // Assign ID as cardIndex
            cardIndex++;

            // Assign 1 Gunslinger Card
            PlayingCard gunslingerCard = cards[cardIndex].GetComponent<PlayingCard>();
            gunslingerCard.SetCardProperties(6, 7); // Assign ID as cardIndex
            cardIndex++;

            // Shuffle the cards to randomize their positions
            int numCardsToShuffle = (currentGameMode == 1) ? cardIndex : cards.Length;

            for (int j = numCardsToShuffle - 1; j > 0; j--)
            {
                int randomIndex = rng.Next(j + 1);
                GameObject temp = cards[j];
                cards[j] = cards[randomIndex];
                cards[randomIndex] = temp;
            }
            // Create the ownership string to synchronize card distribution by player ID
            StringBuilder ownershipBuilder = new StringBuilder();
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i] && playerIDsArray[i] != -1)
                {
                    VRCPlayerApi player = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                    if (_pod.GetPlayerScript(player).dead)
                    {
                        // Debug.Log("Player at seat " + i + " is dead. Skipping card distribution.");
                        continue;
                    }
                    ownershipBuilder.Append(player.playerId + ":");

                    // On bar-fight mode, we only deal 3 cards to each player
                    for (int j = 0; j < 3; j++)
                    {
                        ownershipBuilder.Append(cards[(i * 3) + j].GetComponent<PlayingCard>().cardID + "-" + cards[(i * 3) + j].GetComponent<PlayingCard>().cardValue + "-" + cards[(i * 3) + j].GetComponent<PlayingCard>().cardSuit);
                        if (j < 2) ownershipBuilder.Append(",");
                    }

                    ownershipBuilder.Append(";");

                }
            }
            cardOwnership = ownershipBuilder.ToString();
            dealVersion++;

        }
        else
        {
            // Assign suits and values without random duplication
            int cardIndex = 0;

            // Assign Suits and Values in a deterministic way to avoid duplicates
            // Suits: 0 = Hearts, 1 = Clubs, 2 = Diamonds, 3 = Spades
            // Values: 0 = Queen, 1 = King, 2 = Ace, 3 = Joker

            // Assign 6 Queens
            for (int i = 0; i < 6; i++, cardIndex++)
            {
                PlayingCard playingCard = cards[cardIndex].GetComponent<PlayingCard>();
                int suitIndex = suits[i % suits.Length];
                playingCard.SetCardProperties(0, suitIndex); // Assign ID as cardIndex
            }

            // Assign 6 Kings
            for (int i = 0; i < 6; i++, cardIndex++)
            {
                PlayingCard playingCard = cards[cardIndex].GetComponent<PlayingCard>();
                int suitIndex = suits[i % suits.Length];
                playingCard.SetCardProperties(1, suitIndex); // Assign ID as cardIndex
            }

            // Assign 6 Aces
            for (int i = 0; i < 6; i++, cardIndex++)
            {
                PlayingCard playingCard = cards[cardIndex].GetComponent<PlayingCard>();
                int suitIndex = suits[i % suits.Length];
                playingCard.SetCardProperties(2, suitIndex); // Assign ID as cardIndex
            }

            // Assign 2 Jokers
            for (int i = 0; i < 2; i++, cardIndex++)
            {
                PlayingCard playingCard = cards[cardIndex].GetComponent<PlayingCard>();
                playingCard.SetCardProperties(3, 4); // Value: 3 for Joker, Suit: 4 indicating "Joker"
            }

            // Shuffle the cards to randomize their positions
            int numCardsToShuffle = (currentGameMode == 1) ? cardIndex : cards.Length;
            System.Random random = new System.Random(DateTime.Now.Ticks.GetHashCode());
            for (int j = numCardsToShuffle - 1; j > 0; j--)
            {
                int randomIndex = rng.Next(j + 1);
                GameObject temp = cards[j];
                cards[j] = cards[randomIndex];
                cards[randomIndex] = temp;
            }

            // Create the ownership string to synchronize card distribution by player ID
            StringBuilder ownershipBuilder = new StringBuilder();
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                // Debug.Log("<color=#FFAA55>[CARD DEALER] Seat " + i + " is " + seatStatuses[i] + "</color>");
                if (seatStatuses[i] && playerIDsArray[i] != -1)
                {
                    VRCPlayerApi player = VRCPlayerApi.GetPlayerById(playerIDsArray[i]);
                    if (_pod.GetPlayerScript(player).dead)
                    {
                        // Debug.Log("Player at seat " + i + " is dead. Skipping card distribution.");
                        continue;
                    }
                    ownershipBuilder.Append(player.playerId + ":");
                    for (int j = 0; j < 5; j++)
                    {
                        ownershipBuilder.Append(cards[(i * 5) + j].GetComponent<PlayingCard>().cardID + "-" + cards[(i * 5) + j].GetComponent<PlayingCard>().cardValue + "-" + cards[(i * 5) + j].GetComponent<PlayingCard>().cardSuit);
                        if (j < 4) ownershipBuilder.Append(",");
                    }
                    ownershipBuilder.Append(";");
                }
            }
            cardOwnership = ownershipBuilder.ToString();
            dealVersion++;
        }


    }

    public void DelayedSerialization()
    {
        RequestSerialization();
    }
    public void DelayedSerializationWithOnDeserialization()
    {
        RequestSerialization();
        OnDeserialization();
    }

    public void LocalPlayerSubmitCards()
    {
        // Debug.Log("Local player wants to submit cards");
        _pod.GetLocalPlayerScript().SubmitCards();
    }

    internal void LocalPlayerCallBS()
    {
        // Debug.Log("Local player wants to call BS");
        Player plr = _pod.GetLocalPlayerScript();
        // _pod.GetLocalPlayerScript().CallBS();
        if (Utilities.IsValid(plr))
        {
            plr.CallBS();
        }
    }

    private void DistributeCardsToPlayers()
    {
        // Debug.Log("Distributing cards to players. The string is: " + _l_cardOwnership);

        // 1. Quick check if there's any data
        if (string.IsNullOrEmpty(_l_cardOwnership))
        {
            // Debug.LogWarning("cardOwnership is null or empty—no cards to distribute.");
            return;
        }

        // 2. Split the big string by ';'
        string[] playerCards = _l_cardOwnership.Split(';');
        // Debug.Log("We have card assignments for " + playerCards.Length + " seat/player entries.");

        // We'll track how many cards each seat has, just for some logging
        int[] seatCardCount = new int[4]; // assuming 4 seats

        // 3. Loop over each “playerId:card1,card2,...”
        for (int i = 0; i < playerCards.Length; i++)
        {

            // Empty segment? (sometimes you get a trailing semicolon)
            if (string.IsNullOrEmpty(playerCards[i]))
                continue;

            // Example: "12345:10-1-0,11-1-0,12-2-3..."
            string[] cardAssignments = playerCards[i].Split(':');
            if (cardAssignments.Length < 2)
            {
                // Debug.LogWarning($"Malformed segment (missing colon): {playerCards[i]}");
                continue;
            }

            // Try parse occupant's playerId
            if (!int.TryParse(cardAssignments[0], out int occupantId))
            {
                // Debug.LogWarning($"Could not parse occupantId from '{cardAssignments[0]}'");
                continue;
            }

            // Grab the occupant's seat ID from your seat-tracking system
            // (You need a function or array that you maintain, e.g. seatForPlayer[occupantId] = seatIndex)
            int occupantSeat = GetSeatIDForPlayer(occupantId);
            if (occupantSeat < 0 || occupantSeat >= stations.Length)
            {
                // Debug.LogWarning($"No valid seat found for occupant {occupantId}");
                continue;
            }

            if (seatCardCount[occupantSeat] >= 5) continue;


            // Split the cards after the colon
            string[] cardIDs = cardAssignments[1].Split(',');
            if (cardIDs.Length == 0)
            {
                // Debug.LogWarning($"No cards listed for occupant {occupantId}");
                continue;
            }

            // Debug.Log($"Occupant {occupantId} (seat {occupantSeat}) has {cardIDs.Length} cards.");

            bool isLocalPlayersSeat = (occupantId == localPlayerCache.playerId);

            // We'll keep a small local counter for how many cards we place for occupantSeat
            int localCardIndex = 0;

            // 4. Go through each "id-value-suit"
            foreach (string cardDescriptor in cardIDs)
            {
                if (string.IsNullOrEmpty(cardDescriptor))
                    continue;

                string[] idValueSuit = cardDescriptor.Split('-');
                if (idValueSuit.Length < 3)
                {
                    // Debug.LogWarning($"Malformed card descriptor '{cardDescriptor}'");
                    continue;
                }

                // Attempt to parse each part
                if (!int.TryParse(idValueSuit[0], out int cardID) ||
                    !int.TryParse(idValueSuit[1], out int cardValue) ||
                    !int.TryParse(idValueSuit[2], out int cardSuit))
                {
                    // Debug.LogWarning($"Could not parse numeric parts of '{cardDescriptor}'");
                    continue;
                }

                // 5. Find that card in our 'cards' array
                GameObject foundCard = null;
                PlayingCard pc = null;
                for (int c = 0; c < cards.Length; c++)
                {
                    pc = cards[c].GetComponent<PlayingCard>();
                    if (pc.cardID == cardID)
                    {
                        foundCard = cards[c];
                        break;
                    }
                }

                if (!foundCard)
                {
                    // Debug.LogWarning($"Could not find any card with cardID == {cardID}");
                    continue;
                }

                // 6. Position & ownership logic
                // Transfer network ownership to occupant
                VRCPlayerApi occupantPlayer = VRCPlayerApi.GetPlayerById(occupantId);
                if (occupantPlayer != null && occupantPlayer.IsValid())
                {
                    Networking.SetOwner(occupantPlayer, foundCard);
                }
                else
                {
                    // Debug.LogWarning($"Occupant ID {occupantId} is not a valid player; continuing facedown anyway...");
                }

                // Update the card's data and appearance
                pc.SetCardProperties(cardValue, cardSuit);
                pc.UpdateCardTexture();

                // Determine a local offset so multiple cards don't overlap perfectly
                // e.g., shift them horizontally or vertically
                Vector3 offset = new Vector3((localCardIndex - (currentGameMode == 0 ? 2 : 1)) * 0.15f, 0f, 0f);
                localCardIndex++;
                seatCardCount[occupantSeat]++;

                // Move the card to occupantSeat's anchor
                Transform seatAnchor = cardAnchors[occupantSeat].transform;
                foundCard.transform.position = seatAnchor.TransformPoint(offset);
                foundCard.transform.rotation = seatAnchor.rotation;

                // Decide if we show face or hide
                if (isLocalPlayersSeat)
                {
                    pc._EnableCardRenderers(); // local occupant sees the card face
                    pc.TryToSync();
                }
                else
                {
                    pc.HideCard(); // spectators and other players see facedown
                }
            }
        }

    }

    private int GetSeatIDForPlayer(int occupantId)
    {
        for (var i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] == occupantId)
            {
                return i;
            }
        }
        return -1;
    }


    internal void SubmitCards(int mySeatID, int[] selectedCardValues, int[] cardIDs)
    {
        FlyCards(mySeatID, cardIDs);

        // Debug.Log("Card submission. Did the previous player bullshit? " + didPreviousPlayerBullshit);

        if (didPreviousPlayerBullshit && _IsLocalPlayerTableMaster())
        {
            // Debug.Log("The previous player got away with their bullshit. Lets give them an increase in their stats");
            var po = GetSeatPlayerObject(previousSeatId);

            if (Utilities.IsValid(po))
            {
                po.BluffSuccess();
            }
        }

        // Debug.Log("Okay then, lets see if he is bullshitting or not. THIS SHOULD ONLY BE CALLED ON MASTER");
        // we need to loop through the values, and see if any of them is not currentRoundRandomValue
        bool isBullshit = false;

        lastSubmittedCards = selectedCardValues;

        lastSubmittedCardsString = "";

        for (var i = 0; i < selectedCardValues.Length; i++)
        {
            lastSubmittedCardsString += selectedCardValues[i].ToString() + ";";
        }

        for (int i = 0; i < selectedCardValues.Length; i++)
        {
            // Debug.Log("[TABLE ID: " + tableID + "] Checking card value " + selectedCardValues[i] + " against " + currentRoundRandomValue);
            if (selectedCardValues[i] != currentRoundRandomValue && selectedCardValues[i] != 3) // 3 is the joker
            {
                isBullshit = true;
                break;
            }
        }

        // Debug.Log("Is he bullshit? " + isBullshit);
        didPreviousPlayerBullshit = isBullshit;
        // Debug.Log("At this point, this players turn is over. We need to roll over to the next player");

        if (!isBullshit && _IsLocalPlayerTableMaster())
        {
            if (currentRoundPlayer != -1)
            {
                Player po = GetSeatPlayerObject(currentRoundPlayer);
                if (Utilities.IsValid(po))
                    po.StraightPlay();
            }
        }

        if (cardIDs.Length == 3 && _IsLocalPlayerTableMaster())
        {
            if (currentRoundPlayer != -1)
            {
                Player po = GetSeatPlayerObject(currentRoundPlayer);
                if (Utilities.IsValid(po))
                    po.DaredevilPlay();
            }


        }

        if (cardIDs.Length == 1 && _IsLocalPlayerTableMaster())
        {
            if (currentRoundPlayer != -1)
            {
                Player po = GetSeatPlayerObject(currentRoundPlayer);
                if (Utilities.IsValid(po))
                    po.ScaredyCatPlay();
            }
        }

        // Define the counter-clockwise seat arrangement
        var nextSeatID = FindNextPlayer(mySeatID);

        // Debug.Log("Next Seat ID is " + nextSeatID);

        previousSeatId = mySeatID;
        currentRoundPlayer = nextSeatID;
        currentRoundPlayerVersion++;
        currentRoundTurn++;

        // we need to check if there is only one player remaining with cards in hand
        int playersWithCards = 0;
        int lastPlayerWithCards = -1;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i] && GetSeatPlayerObject(i).cardsLeftInHand > 0 && !GetSeatPlayerObject(i).dead)
            {
                playersWithCards++;
                lastPlayerWithCards = i;
            }
        }
        // Debug.Log("Players with cards left: " + playersWithCards);
        if (playersWithCards == 1)
        {
            // Debug.Log("Only one player remaining with cards. They are forced to call BS");
            forcedBSCall = lastPlayerWithCards;
            if (_IsLocalPlayerTableMaster())
            {
                SendCustomEventDelayedSeconds("DelayedSerializationWithOnDeserialization", 1f);
            }
            return;
        }

        RequestSerialization();

        if (_IsLocalPlayerTableMaster())
        {
            OnDeserialization();
        }
    }

    public int FindNextPlayer(int startSeatID, bool masterSwap = false)
    {
        int[] seatOrderFromBottomCCW = { 0, 2, 1, 3 };

        int nextSeatID = -1;

        for (int i = 0; i < seatOrderFromBottomCCW.Length; i++)
        {
            if (seatOrderFromBottomCCW[i] == startSeatID)
            {
                // Debug.Log("Found my seat. It is at index " + i + " and the value is " + startSeatID);
                for (int j = 1; j < seatOrderFromBottomCCW.Length; j++)
                {
                    int potentialNextSeatID = seatOrderFromBottomCCW[(i + j) % seatOrderFromBottomCCW.Length];
                    Debug.Log("<color=green>Checking seat " + potentialNextSeatID + " for next player.</color>");
                    // Lets dump all the conditions from below into the logs
                    Debug.Log("Showdown Deaths: " + showdownDeaths[potentialNextSeatID]);
                    Debug.Log("Seat Status: " + seatStatuses[potentialNextSeatID]);
                    // if (GetSeatPlayerObject(potentialNextSeatID) == null)
                    // {
                    //     // Debug.Log("Player Object is null");
                    // }
                    // else
                    // {
                    //     // Debug.Log("Cards Left in Hand: " + GetSeatPlayerObject(potentialNextSeatID).cardsLeftInHand);
                    //     // Debug.Log("Dead: " + GetSeatPlayerObject(potentialNextSeatID).dead);
                    // }
                    // Debug.Log("Player ID: " + playerIDsArray[potentialNextSeatID]);

                    if (
                        !showdownDeaths[potentialNextSeatID]
                        && seatStatuses[potentialNextSeatID]
                        && GetSeatPlayerObject(potentialNextSeatID).cardsLeftInHand > 0
                        && !GetSeatPlayerObject(potentialNextSeatID).dead
                        && playerIDsArray[potentialNextSeatID] != -1
                        && Array.IndexOf(deadPlayerIDsArray, potentialNextSeatID) == -1
                    )
                    {
                        if (masterSwap && potentialNextSeatID == previousSeatId)
                        {
                            continue;
                        }
                        nextSeatID = potentialNextSeatID;
                        break;
                    }
                }
            }

            if (nextSeatID != -1)
            {
                // Debug.Log("Next player is at seat: " + nextSeatID);
                break;
            }
        }

        Debug.Log("RETURNING SEAT ID: " + nextSeatID);
        return nextSeatID;
    }


    internal void FlyCards(int mySeatID, int[] cardIDs)
    {
        // we need to find each card by the IDs and set the deckTransform and the boolean to start their flight
        for (int i = 0; i < cardIDs.Length; i++)
        {
            for (int j = 0; j < cards.Length; j++)
            {
                if (cards[j].GetComponent<PlayingCard>().cardID == cardIDs[i])
                {
                    cards[j].GetComponent<PlayingCard>().deckTransform = deckObjectTransform;
                    cards[j].GetComponent<PlayingCard>().flyingToDeck = true;
                }
            }
        }
    }

    internal void _DisplayStatusMessage(string v, bool longMessage = false)
    {
        statusText.text = v;
        if (longMessage)
        {
            statusAnimator.speed = 0.5f;
        }
        else
        {
            // statusText.fontSize = 36;
            statusAnimator.speed = 1f;
        }
        statusAnimator.SetTrigger("Reveal");
    }

    public void RevealLastCards()
    {
        Debug.Log("Going to network call to reveal last cards. Am I master? " + _IsLocalPlayerTableMaster());

        if (_IsLocalPlayerTableMaster())
        {
            if (lastRoundQuit)
            {
                Debug.Log("Last round quit. Not doing this.");
                return;
            }
            bRevealCards = true;
            RequestSerialization();
            OnDeserialization();
        }
        else
        {
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "RevealLastCards");
        }
    }

    public void NRevealLastCards()
    {
        // Debug.Log("Revealing last cards. Total: " + lastSubmittedCards.Length);

        if (lastSubmittedCards.Length == 1 && lastSubmittedCards[0] == 4)
        {
            // Debug.Log("Devil card played, and called!");
            deckAudio.PlayOneShot(oneShotClips[5]);
            devilCardCalled = true;
        }
        if (lastSubmittedCards.Length == 1 && lastSubmittedCards[0] == 7)
        {
            // Debug.Log("Showdown card played, and called!");
            deckAudio.PlayOneShot(oneShotClips[9]);
            quickdrawCalled = true;
        }

        for (int i = 0; i < lastSubmittedCards.Length; i++)
        {
            // random suit between 0 and 3
            int randomSuit = UnityEngine.Random.Range(0, 3);
            revealCards[i].GetComponent<PlayingCard>().SetCardProperties(lastSubmittedCards[i], randomSuit); // 4 is the suit for joker
            revealCards[i].GetComponent<PlayingCard>().UpdateCardTexture();
            revealCards[i].GetComponent<PlayingCard>()._EnableCardRenderers();
            revealCards[i].GetComponent<PlayingCard>().ResetColour();

            if (currentGameMode == 1)
            {
                // for cards 5 and 6, we do not change the colour. Foreverything else, we do.

                if (lastSubmittedCards[i] == 5 || lastSubmittedCards[i] == 6)
                {
                    if (lastSubmittedCards[i] == 5) // showdown card
                    {
                        deckAudio.PlayOneShot(oneShotClips[6]);
                        // roundText.text = "Showdown! Everyone shoots!";
                        showdownObject.SetActive(true);
                    }
                    else if (lastSubmittedCards[i] == 6) // gunslinger card
                    {
                        deckAudio.PlayOneShot(oneShotClips[7]);

                    }
                    continue;
                }
                else if (lastSubmittedCards[i] == 7)
                {

                }
                else
                {
                    if (lastSubmittedCards[i] == currentRoundRandomValue || lastSubmittedCards[i] == 3)
                    {
                        revealCards[i].GetComponent<PlayingCard>().ColourGreen();
                    }
                    else
                    {
                        revealCards[i].GetComponent<PlayingCard>().ColourRed();
                    }
                }
            }
            else
            {

                if (lastSubmittedCards[i] == currentRoundRandomValue || lastSubmittedCards[i] == 3)
                {
                    revealCards[i].GetComponent<PlayingCard>().ColourGreen();
                }
                else
                {
                    revealCards[i].GetComponent<PlayingCard>().ColourRed();
                }
            }
        }

        SendCustomEventDelayedSeconds("CheckBSCall", 1);
    }

    public void CheckBSCall()
    {
        if (!_IsLocalPlayerTableMaster())
        {
            // Debug.Log("We are not the master. We aint checking shit.");
            return;
        }

        if (lastRoundQuit || currentRoundPlayer == -1)
        {
            // Debug.Log("Last round quit: " + lastRoundQuit + ", currentRoundPlayer: " + currentRoundPlayer);
            return;
        }

        bool correctCall = false;

        if (currentGameMode == 0)
        {
            if (Array.IndexOf(lastSubmittedCards, 3) != -1)
            {
                // Last player gets the joker achievement!

                var pspo = GetSeatPlayerObject(previousSeatId);

                if (Utilities.IsValid(pspo))
                {
                    GetSeatPlayerObject(previousSeatId).StoreAprils2025();
                    GetSeatPlayerObject(previousSeatId).StoreWildcardPlay();
                }
            }
            if (lastSubmittedCards.Length == 1 && lastSubmittedCards[0] == 4)
            {
                for (var i = 0; i < seatStatuses.Length; i++)
                {
                    //Debug.Log($"Seat {i + 1}: " + seatStatuses[i]);
                    if (seatStatuses[i])
                    {
                        if (GetSeatPlayerObject(i).mySeatID == previousSeatId || GetSeatPlayerObject(i).cardsLeftInHand == 0) continue;
                        devilCardShooters += GetSeatPlayerObject(i).mySeatID + ";";
                        devilCardshots++;
                    }
                }

                devilCardCalled = true;

                // Debug.Log("Shooters: " + devilCardShooters);

                GetSeatPlayerObject(previousSeatId).BluffFailure();

                // Debug.Log("Shooter IDs: " + devilCardShooters);
            }
            else
            {

                if (didPreviousPlayerBullshit)
                {
                    // Debug.Log("Good call. Previous seat has to go to russian roulette");
                    correctCall = true;
                }
                else
                {
                    // Debug.Log("Bad call. You have to go to russian roulette");
                    correctCall = false;
                }

                if (correctCall)
                {
                    if (_IsLocalPlayerTableMaster())
                    {
                        GetSeatPlayerObject(currentRoundPlayer).GetComponent<Player>().BSCallSuccess();
                        GetSeatPlayerObject(previousSeatId).GetComponent<Player>().BluffFailure();
                    }
                    rouletteTarget = previousSeatId;
                }
                else
                {
                    if (_IsLocalPlayerTableMaster())
                    {
                        GetSeatPlayerObject(currentRoundPlayer).GetComponent<Player>().BSCallFail();
                    }
                    rouletteTarget = BSCaller;
                }
            }
        }
        else if (currentGameMode == 1)
        {
            // We have a different set of rules for showdown mode

            // lets check if the card is one of the special ones (value 5 or 6)

            if (lastSubmittedCards.Length == 1 && (lastSubmittedCards[0] == 5 || lastSubmittedCards[0] == 6 || lastSubmittedCards[0] == 7))
            {
                // Debug.Log("One of the showdown cards was called, which one?");

                if (lastSubmittedCards[0] == 5)
                {
                    // Debug.Log("showdown card was called. Everyone, prepare your guns!");
                    showdownCalled = true;
                    // Lets find how many players are left

                    int playersLeft = 0;

                    for (int i = 0; i < seatStatuses.Length; i++)
                    {
                        if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
                        {
                            playersLeft++;
                        }
                    }

                    showdownShooters = playersLeft;

                }
                else if (lastSubmittedCards[0] == 6)
                {
                    // Debug.Log("Gunslinger card was called. Whoever played this card, prepare your gun!");
                    gunslingerCalled = true;
                    RotateTurnIndicator(previousSeatId);
                }
                else if (lastSubmittedCards[0] == 7)
                {
                    Debug.Log("Quick draw!");
                    quickdrawCalled = true;
                    quickDrawShotsLeft = 2;
                }
            }
            else
            {
                if (didPreviousPlayerBullshit)
                {
                    // Debug.Log("Good call. Previous seat has to go to russian roulette");
                    correctCall = true;

                    // The other player gets to shoot whoever they want with gunslinger mode
                }
                else
                {
                    // Debug.Log("Bad call. You have to go to russian roulette");
                    correctCall = false;

                    // A self shot takes place
                }

                if (correctCall)
                {
                    if (_IsLocalPlayerTableMaster())
                    {
                        if (currentRoundPlayer != -1)
                            GetSeatPlayerObject(currentRoundPlayer).BSCallSuccess();
                        if (previousSeatId != -1)
                            GetSeatPlayerObject(previousSeatId).BluffFailure();
                    }
                    rouletteTarget = BSCaller;
                    bCorrectCallShowdown = true;
                }
                else
                {
                    if (_IsLocalPlayerTableMaster())
                        if (currentRoundPlayer != -1)
                        {
                            Player seatPO = GetSeatPlayerObject(currentRoundPlayer);
                            if (Utilities.IsValid(seatPO))
                                seatPO.BSCallFail();
                        }
                    rouletteTarget = BSCaller;
                }
            }
        }

        lastSubmittedCardsString = "";


        RequestSerialization();
        if (_IsLocalPlayerTableMaster())
        {
            OnDeserialization();
        }
    }

    internal void CallBS(int mySeatID)
    {
        // Debug.Log("Seat ID " + mySeatID + " called BS.");
        _DisplayStatusMessage("Bullshit!");

        BSCaller = mySeatID;
        RequestSerialization();

        SendCustomEventDelayedSeconds("RevealLastCards", 3);
    }

    public override void OnPostSerialization(SerializationResult result)
    {
        if (result.success)
        {
            // Debug.Log("Serialization successful.");
        }
        else
        {
            Debug.LogError("Serialization failed.");
        }
    }

    public void ToggleQuickdrawMode()
    {
        if (!_IsLocalPlayerTableMaster())
        {
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "ToggleQuickdrawMode");
            quickdrawSwitchAnimator.SetBool("SwitchOn", !quickdrawEnabled);
            return;
        }
        else
        {
            quickdrawEnabled = !quickdrawEnabled;
            quickdrawSwitchAnimator.SetBool("SwitchOn", quickdrawEnabled);
            RequestSerialization();
        }
    }

    public void ToggleDevilsCardMode()
    {
        if (!_IsLocalPlayerTableMaster())
        {
            // Debug.Log("I am not the master. I am sending the request to the owner.");
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "ToggleDevilsCardMode");
            devilsCardSwitchAnimator.SetBool("SwitchOn", !devilCardEnabled);
            return;
        }
        else
        {
            // Debug.Log("I am the master. I am toggling the devil card mode.");
            devilCardEnabled = !devilCardEnabled;
            devilsCardSwitchAnimator.SetBool("SwitchOn", devilCardEnabled);
            RequestSerialization();
        }
    }

    // public void ResolveQuickDrawDeaths()
    // {

    //     string[] qdDeadPlayers = QDDeaths.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
    //     string[] qdShotTimes = QDTimes.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
    //     string[] qdShooter = QDShooters.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

    //     // if there is more than 1 dead player at this point, we need to figure out who shot first (which time is lower)
    //     if (qdDeadPlayers.Length > 1)
    //     {
    //         // Debug.Log("There are more than 1 dead players. We need to resolve the deaths.");
    //         // we need to sort the arrays by the shot times
    //         for (int i = 0; i < qdShotTimes.Length; i++)
    //         {
    //             for (int j = i + 1; j < qdShotTimes.Length; j++)
    //             {
    //                 if (double.Parse(qdShotTimes[i]) > double.Parse(qdShotTimes[j]))
    //                 {
    //                     // swap the values in all three arrays
    //                     string temp = qdDeadPlayers[i];
    //                     qdDeadPlayers[i] = qdDeadPlayers[j];
    //                     qdDeadPlayers[j] = temp;

    //                     temp = qdShotTimes[i];
    //                     qdShotTimes[i] = qdShotTimes[j];
    //                     qdShotTimes[j] = temp;

    //                     temp = qdShooter[i];
    //                     qdShooter[i] = qdShooter[j];
    //                     qdShooter[j] = temp;
    //                 }
    //             }
    //         }

    //         // now we have the players sorted by shot time, so we can kill just the first player

    //         Debug.Log("First player to die: " + qdDeadPlayers[0] + ", shot by: " + qdShooter[0] + ", time: " + qdShotTimes[0]);

    //         int seatID = GetSeatIDForPlayer(int.Parse(qdShooter[0]));
    //         int pidForShotTarget = int.Parse(qdDeadPlayers[0]);
    //         deadPlayerIDs = "" + VRCPlayerApi.GetPlayerById(pidForShotTarget).playerId + ";";

    //         if (int.Parse(qdDeadPlayers[0]) == qdChallenger || int.Parse(qdDeadPlayers[0]) == qdChallengeTarget)
    //             PlayerDied(int.Parse(qdDeadPlayers[0]), seatID);


    //     }
    //     else
    //     {
    //         Debug.Log("Only one death, lets kill em. " + qdDeadPlayers[0] + " shot by: " + qdShooter[0] + ", time: " + qdShotTimes[0]);

    //         int seatID = GetSeatIDForPlayer(int.Parse(qdShooter[0]));
    //         int pidForShotTarget = 
    //         deadPlayerIDs = "" + VRCPlayerApi.GetPlayerById(pidForShotTarget).playerId + ";";

    //         Debug.Log("Setting dead player IDs to: " + deadPlayerIDs + " QDShooter0: " + qdShooter[0] + " QDDeadPlayer0: " + qdDeadPlayers[0]);

    //         if (int.Parse(qdDeadPlayers[0]) == qdChallenger || int.Parse(qdDeadPlayers[0]) == qdChallengeTarget)
    //             PlayerDied(int.Parse(qdDeadPlayers[0]), seatID);

    //     }

    //     if (quickDrawShotsLeft == 0)
    //     {
    //         int playersLeft = 0;

    //         for (int i = 0; i < seatStatuses.Length; i++)
    //         {
    //             if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
    //             {
    //                 playersLeft++;
    //             }
    //         }

    //         if (playersLeft == 1 && gameStatus != 5)
    //         {
    //             Debug.Log("Only one player left. They are the winner. ID: " + GetLastPlayerStanding());
    //             gameWinner = GetLastPlayerStanding();
    //             gameStatus = 5;

    //             RequestSerialization();
    //             OnDeserialization();
    //             if (!gameOverTimer)
    //             {
    //                 gameOverTimer = true;
    //                 SendCustomEventDelayedSeconds("GameOver", 6f);
    //             }
    //             return;
    //         }
    //         else
    //         {
    //             // Reset and start next round
    //             System.Random random = new System.Random(DateTime.Now.Ticks.GetHashCode());
    //             int randomSeat = random.Next(4);

    //             while (!seatStatuses[randomSeat] || GetSeatPlayerObject(randomSeat).dead || Array.IndexOf(deadPlayerIDsArray, randomSeat) != -1)
    //             {
    //                 if (gameStatus == 5 || !gameStarted)
    //                 {
    //                     // Debug.Log("Game is over or not started. Exiting.");
    //                     return;
    //                 }
    //                 playersLeft = 0;
    //                 for (int i = 0; i < seatStatuses.Length; i++)
    //                 {
    //                     if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
    //                     {
    //                         playersLeft++;
    //                     }
    //                 }
    //                 if (playersLeft == 1 && gameStatus != 5)
    //                 {
    //                     Debug.Log("Only one player left. They are the winner. ID: " + GetLastPlayerStanding());
    //                     gameWinner = GetLastPlayerStanding();
    //                     gameStatus = 5;

    //                     RequestSerialization();
    //                     OnDeserialization();
    //                     if (!gameOverTimer)
    //                     {
    //                         gameOverTimer = true;
    //                         SendCustomEventDelayedSeconds("GameOver", 6f);
    //                     }
    //                     return;
    //                 }
    //                 randomSeat = random.Next(4);
    //                 // Debug.Log("Players left: " + playersLeft);
    //             }

    //             currentRoundPlayer = randomSeat;
    //             currentRoundPlayerVersion++;
    //             currentRoundRandomValue = -1;
    //             didPreviousPlayerBullshit = false;
    //             lastSubmittedCardsString = "";
    //             bRevealCards = false;
    //             cardOwnership = "";
    //             _l_cardOwnership = "";
    //             forcedBSCall = -1;
    //             devilCardID = -1;
    //             _l_devilCardID = -1;
    //             gameStatus = 4;
    //             quickdrawCalled = false;
    //             qdChallenger = -1;
    //             qdDrawTime = -1.0;
    //             QDDeaths = "";
    //             QDShooters = "";
    //             QDTimes = "";
    //             qdChallengeTarget = -1;
    //             currentRound++;


    //             RequestSerialization();
    //             OnDeserialization();
    //             if (!startNextRoundTimer && !showdownCalled)
    //             {
    //                 Debug.Log("<color=#76f59a>StartNextRound from ShotFired</color>");
    //                 startNextRoundTimer = true;
    //                 SendCustomEventDelayedSeconds("StartNextRound", 3f);
    //             }

    //             if (_IsLocalPlayerTableMaster() && gameStarted && gameStatus != 5 && currentRound != -1)
    //             {
    //                 // Debug.Log("gameStarted: " + gameStarted + ", gameStatus: " + gameStatus + ", currentRound: " + currentRound);
    //                 deckAudio.PlayOneShot(oneShotClips[3]);
    //             }

    //         }

    //     }
    //     else
    //     {
    //         Debug.Log("We have still shots left to resolve...");
    //     }

    // }

    private bool _IsTheGameADraw()
    {
        int playersLeft = 0;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i] && !showdownDeaths[i])
            {
                playersLeft++;
            }
        }

        if (playersLeft == 0)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    private bool _IsGameOverBecauseOnePlayerLeft()
    {
        int playersLeft = 0;

        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i] && !showdownDeaths[i])
            {
                playersLeft++;
            }
        }

        if (playersLeft == 1 && gameStatus != 5)
        {
            Debug.Log("Only one player left. They are the winner. ID: " + GetLastPlayerStanding());
            gameWinner = GetLastPlayerStanding();
            gameStatus = 5;

            RequestSerialization();
            OnDeserialization();
            if (!gameOverTimer)
            {
                gameOverTimer = true;
                SendCustomEventDelayedSeconds("GameOver", 6f);
            }
            return true;
        }
        else
        {
            return false;
        }
    }

    public void _StartNextRoundAfterAShot(bool needNewNextPlayer = false)
    {
        if (!_IsLocalPlayerTableMaster())
            return;
        if (gameStatus == 5)
            return;

        if (!startNextRoundTimer)
        {

            if (needNewNextPlayer)
            {
                currentRoundPlayer = FindNextPlayer(currentRoundPlayer);
            }

            currentRound++;
            currentRoundTurn = 0;
            devilCardCalled = false;
            showdownCalled = false;
            showdownGunsPickedUp = 0;
            gunslingerCalled = false;
            bCorrectCallShowdown = false;

            currentRoundRandomValue = -1;
            currentRoundPlayer = -1;
            currentRoundPlayerVersion++;
            didPreviousPlayerBullshit = false;
            lastSubmittedCardsString = "";
            bRevealCards = false;
            quickdrawCalled = false;

            cardOwnership = "";
            _l_cardOwnership = "";
            forcedBSCall = -1;
            devilCardID = -1;
            _l_devilCardID = -1;
            gameStatus = 4;

            gunsPickedUpForShowdown = new bool[4] { false, false, false, false };
            showdownDeaths = new bool[4] { false, false, false, false };

            if (_IsLocalPlayerTableMaster() && gameStarted && gameStatus != 5 && currentRound != -1)
            {
                // Debug.Log("gameStarted: " + gameStarted + ", gameStatus: " + gameStatus + ", currentRound: " + currentRound);
                deckAudio.Stop();
                deckAudio.PlayOneShot(oneShotClips[3]);
            }
            RequestSerialization();
            OnDeserialization();
            Debug.Log("<color=#76f59a>StartNextRound from _StartNextRoundAfterAShot</color>");
            startNextRoundTimer = true;
            SendCustomEventDelayedSeconds("StartNextRound", 3f);
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="shotKilled">Did the shot kill?</param>
    /// <param name="shotTarget">The shot target Seat ID. In the case of a suicide, this can be -1</param>
    /// <param name="suicide">Was it a suicide?</param>
    /// <param name="shooterID">The shooter's player ID</param>
    [NetworkCallable(maxEventsPerSecond: 12)]
    public void ShotFired(bool shotKilled, int shotTarget, bool suicide, int shooterID)
    {
        if (!_IsLocalPlayerTableMaster()) return;
        if (gameStatus == 5) return;
        if (!gameStarted) return;

        if (Networking.LocalPlayer.displayName == "aRkker")
            Debug.Log("The shot was fired. Fatal?: " + shotKilled + ", target: " + shotTarget + ", suicide: " + suicide + ", shooters Player ID: " + shooterID + " devilsCard called: " + devilCardCalled + " Dshots remaining: " + devilCardshots + " shots taken: " + devilCardshotsTaken);

        // Early exit check once instead of multiple times
        if (gameStatus == 5) return;

        // Branch based on game mode - keep everything inline
        if (currentGameMode == 0)
        {
            // CLASSIC MODE - existing logic (inline)
            if (devilCardEnabled && devilCardCalled && devilCardshots > 0)
            {
                if (shotKilled)
                {
                    devilCardKillsThisRound++;
                    _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                    PlayerDied(GetSeatIDForPlayer(shooterID), shooterID, previousSeatId);
                }
                devilCardshots--;

                if (devilCardshots > 0)
                {
                    if (_IsGameOverBecauseOnePlayerLeft()) return;
                }
                else
                {
                    if (devilCardKillsThisRound == 3)
                    {
                        var devilsAdvocateGainer = GetLastPlayerStanding();
                        GetSeatPlayerObject(devilsAdvocateGainer).StoreDevilsAdvocate();
                    }

                    if (_IsGameOverBecauseOnePlayerLeft())
                    {
                    }
                    else
                    {
                        _StartNextRoundAfterAShot(true);
                    }
                }
            }
            else
            {
                if (shotKilled)
                {
                    _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                    PlayerDied(GetSeatIDForPlayer(shooterID), shooterID, BSCaller);
                }

                if (_IsGameOverBecauseOnePlayerLeft())
                {
                }
                else
                {
                    if (shotKilled)
                    {
                        _StartNextRoundAfterAShot(false);
                    }
                    else
                    {
                        currentRoundPlayer = rouletteTarget;
                        _StartNextRoundAfterAShot(false);
                    }
                }
            }
        }
        else if (currentGameMode == 1)
        {
            // Check quickdraw early exit ONCE
            bool isQuickdrawActive = quickdrawCalled && (qdChallengerDead || qdChallengeTargetDead);

            if (isQuickdrawActive)
            {
                if (Networking.LocalPlayer.displayName == "aRkker")
                    Debug.Log("Ignoring shot - quickdraw already resolved with a death. Waiting for game state update.");
                return;
            }

            if (showdownCalled)
            {
                // SHOWDOWN MODE - inline logic
                if (shotKilled && !suicide && shotTarget != -1)
                {
                    showdownTargetCounts[shotTarget]++;
                }

                if (suicide && shotKilled)
                {
                    int shooterSeat = GetSeatIDForPlayer(shooterID);
                    if (shooterSeat != -1)
                    {
                        showdownDeaths[shooterSeat] = true;
                        thisRoundShowdownDeaths = thisRoundShowdownDeaths + 1;
                    }
                }
                else if (shotKilled && shotTarget != -1)
                {
                    showdownDeaths[shotTarget] = true;
                    thisRoundShowdownDeaths = thisRoundShowdownDeaths + 1;
                }

                showdownShooters--;

                if (showdownShooters > 0)
                {
                    // More shots coming
                }
                else
                {
                    // All shots done - process results inline
                    int totalShots = 0;
                    for (int i = 0; i < showdownTargetCounts.Length; i++)
                    {
                        totalShots += showdownTargetCounts[i];
                        if (showdownTargetCounts[i] == 3 && totalShots == 3)
                        {
                            var targetPlayer = GetSeatPlayerObject(i);
                            if (Utilities.IsValid(targetPlayer))
                            {
                                targetPlayer.StoreContinental();
                            }
                        }
                    }

                    // Clear target counts
                    for (int i = 0; i < showdownTargetCounts.Length; i++)
                    {
                        showdownTargetCounts[i] = 0;
                    }

                    // Process deaths
                    for (int i = 0; i < showdownDeaths.Length; i++)
                    {
                        if (showdownDeaths[i])
                        {
                            GetSeatPlayerObject(i).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                            if (thisRoundShowdownDeaths == 4)
                            {
                                GetSeatPlayerObject(i).persistentStats.SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StoreThenThereWereNone");
                            }
                            PlayerDied(i, -1);
                        }
                    }

                    if (_IsGameOverBecauseOnePlayerLeft())
                    {
                        return;
                    }
                    else
                    {
                        if (_IsTheGameADraw())
                        {
                            if (thisRoundShowdownDeaths == 4)
                            {
                                for (var i = 0; i < playerIDsArray.Length; i++)
                                {
                                    if (playerIDsArray[i] != -1)
                                    {
                                        GetSeatPlayerObject(i).persistentStats.SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StoreThenThereWereNone");
                                    }
                                }

                                if (_IsLocalPlayerTableMaster() && IsLocalPlayerSeatedAtThisTable())
                                {
                                    _pod.GetLocalPlayerScript().persistentStats.SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StoreThenThereWereNone");
                                }
                            }
                            gameDraw = true;
                            gameStatus = 5;
                            RequestSerialization();
                            OnDeserialization();
                            if (!gameOverTimer)
                            {
                                gameOverTimer = true;
                                SendCustomEventDelayedSeconds("GameOver", 6f);
                            }
                            return;
                        }
                        else
                        {
                            _StartNextRoundAfterAShot(true);
                        }
                    }
                }
            }
            else if (quickdrawCalled)
            {
                // QUICKDRAW MODE - optimized inline logic
                // Store original values to reduce repeated property access
                bool qd_challengerDead = qdChallengerDead;
                bool qd_challengeTargetDead = qdChallengeTargetDead;
                int qd_shotsLeft = quickDrawShotsLeft;
                int qd_challenger = qdChallenger;
                int qd_challengeTarget = qdChallengeTarget;
                bool qd_awardTenPaces = qdAwardTenPaces;

                // Early exits
                if (qd_challengerDead || qd_challengeTargetDead) return;
                if (qd_shotsLeft <= 0) return;

                // Track if we need to sync state at the end
                bool needsStatSync = false;
                bool needsGameStateCheck = false;

                // Process shot
                if (shotKilled && shotTarget != -1)
                {
                    // Valid kill - check if it's a quickdraw participant
                    if (shotTarget == qd_challengeTarget || shotTarget == qd_challenger)
                    {
                        // Quickdraw resolved with a kill
                        quickDrawShotsLeft = 0;
                        needsStatSync = true;
                        needsGameStateCheck = true;

                        if (qd_awardTenPaces)
                        {
                            GetSeatPlayerObject(qd_challenger, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
                            GetSeatPlayerObject(qd_challengeTarget, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
                            qdAwardTenPaces = false;
                        }

                        // Track stats based on who shot whom
                        if (shotTarget == qd_challengeTarget)
                        {
                            var plr = GetSeatPlayerObject(qd_challenger);
                            if (Utilities.IsValid(plr))
                            {
                                plr.StoreQDKill();
                            }
                            qdChallengeTargetDead = true;
                            GetSeatPlayerObject(shotTarget).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                            _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IncremengMatchQDChallengeKills");
                            _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IncrementQDTableWins");
                            PlayerDied(shotTarget, shooterID);
                        }
                        else // shotTarget == qd_challenger
                        {
                            var plr = GetSeatPlayerObject(qd_challengeTarget);
                            if (Utilities.IsValid(plr))
                            {
                                plr.StoreQDDefense();
                            }
                            qdChallengerDead = true;
                            GetSeatPlayerObject(shotTarget).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                            _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IncrementQDTableWins");
                            PlayerDied(shotTarget, shooterID);
                        }
                    }
                    else
                    {
                        // Hit someone else - decrement shots
                        quickDrawShotsLeft--;
                        if (quickDrawShotsLeft <= 0)
                        {
                            needsGameStateCheck = true;
                            if (qd_awardTenPaces)
                            {
                                GetSeatPlayerObject(qd_challenger, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
                                GetSeatPlayerObject(qd_challengeTarget, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
                                qdAwardTenPaces = false;
                            }
                        }
                    }
                }
                else
                {
                    // Miss - decrement shots
                    quickDrawShotsLeft--;
                    if (quickDrawShotsLeft <= 0)
                    {
                        needsGameStateCheck = true;
                        if (qd_awardTenPaces)
                        {
                            GetSeatPlayerObject(qd_challenger, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
                            GetSeatPlayerObject(qd_challengeTarget, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
                            qdAwardTenPaces = false;
                        }
                    }
                }

                // CRITICAL: Only sync state once at the end
                if (needsStatSync)
                {
                    RequestSerialization();
                    OnDeserialization();
                }

                // Schedule delayed game state check if needed
                if (needsGameStateCheck)
                {
                    SendCustomEventDelayedSeconds("CheckQuickdrawGameState", 0.2f);
                }
            }
            else
            {
                // REGULAR DUEL MODE
                if (qdChallengerDead || qdChallengeTargetDead)
                {
                    Debug.Log("Ignoring regular/gunslinger shot - we just resolved a quickdraw with a death");
                    return;
                }

                if (shotKilled && !suicide && shotTarget == -1)
                {
                    _StartNextRoundAfterAShot(true);
                    return;
                }

                if (shotKilled)
                {
                    if (suicide)
                    {
                        _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                        PlayerDied(GetSeatIDForPlayer(shooterID), shooterID);
                    }
                    else
                    {
                        GetSeatPlayerObject(shotTarget).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                        PlayerDied(shotTarget, shooterID);
                    }
                }

                if (_IsGameOverBecauseOnePlayerLeft())
                {
                }
                else
                {
                    _StartNextRoundAfterAShot();
                }
            }
        }
    }
    // [NetworkCallable(maxEventsPerSecond: 12)] // Should never receive more than 4 calls per second (4 players)
    // public void ShotFired(bool shotKilled, int shotTarget, bool suicide, int shooterID)
    // {
    //     if (!_IsLocalPlayerTableMaster()) return;
    //     if (gameStatus == 5) return;
    //     if (!gameStarted) return;

    //     if (Networking.LocalPlayer.displayName == "aRkker")
    //         Debug.Log("The shot was fired. Fatal?: " + shotKilled + ", target: " + shotTarget + ", suicide: " + suicide + ", shooters Player ID: " + shooterID + " devilsCard called: " + devilCardCalled + " Dshots remaining: " + devilCardshots + " shots taken: " + devilCardshotsTaken);

    //     if (gameStatus == 5)
    //     {
    //         if (Networking.LocalPlayer.displayName == "aRkker")
    //             Debug.Log("Game is over. Exiting.");
    //         return;
    //     }

    //     if (currentGameMode == 1 && quickdrawCalled && (qdChallengerDead || qdChallengeTargetDead))
    //     {
    //         if (Networking.LocalPlayer.displayName == "aRkker")
    //             Debug.Log("Ignoring shot - quickdraw already resolved with a death. Waiting for game state update.");
    //         return;
    //     }

    //     // Lets branch based on which game mode we are doing
    //     if (currentGameMode == 0)
    //     {
    //         // Now, for the Classic mode, we need to differentiate between a devil's card moment, and a regular moment
    //         if (devilCardEnabled && devilCardCalled && devilCardshots > 0)
    //         {
    //             if (shotKilled)
    //             {
    //                 devilCardKillsThisRound++;
    //                 _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");

    //                 PlayerDied(GetSeatIDForPlayer(shooterID), shooterID, previousSeatId);
    //             }
    //             devilCardshots--;

    //             if (devilCardshots > 0)
    //             {
    //                 if (_IsGameOverBecauseOnePlayerLeft())
    //                 {
    //                     return;
    //                 }
    //             }
    //             else
    //             {

    //                 if (devilCardKillsThisRound == 3) // 3 FOR LIVE
    //                 {
    //                     var devilsAdvocateGainer = GetLastPlayerStanding();
    //                     GetSeatPlayerObject(devilsAdvocateGainer).StoreDevilsAdvocate();
    //                 }

    //                 if (_IsGameOverBecauseOnePlayerLeft())
    //                 {
    //                 }
    //                 else
    //                 {
    //                     _StartNextRoundAfterAShot(true);
    //                 }
    //             }
    //         }
    //         else
    //         {
    //             if (shotKilled)
    //             {
    //                 _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");

    //                 PlayerDied(GetSeatIDForPlayer(shooterID), shooterID, BSCaller);
    //             }

    //             if (_IsGameOverBecauseOnePlayerLeft())
    //             {
    //             }
    //             else
    //             {
    //                 if (shotKilled)
    //                 {
    //                     _StartNextRoundAfterAShot(false);
    //                 }
    //                 else
    //                 {
    //                     currentRoundPlayer = rouletteTarget;
    //                     _StartNextRoundAfterAShot(false);
    //                 }

    //             }
    //         }
    //     }
    //     else if (currentGameMode == 1)
    //     {
    //         if (showdownCalled)
    //         {

    //             if (shotKilled && !suicide && shotTarget != -1)
    //             {
    //                 showdownTargetCounts[shotTarget]++;
    //             }

    //             if (suicide && shotKilled)
    //             {
    //                 int shooterSeat = GetSeatIDForPlayer(shooterID);
    //                 if (shooterSeat != -1)
    //                 {
    //                     showdownDeaths[shooterSeat] = true;
    //                     thisRoundShowdownDeaths = thisRoundShowdownDeaths + 1;
    //                 }
    //             }
    //             else if (shotKilled && shotTarget != -1)
    //             {
    //                 showdownDeaths[shotTarget] = true;
    //                 thisRoundShowdownDeaths = thisRoundShowdownDeaths + 1;
    //             }

    //             showdownShooters--;

    //             if (showdownShooters > 0)
    //             {
    //             }
    //             else
    //             {

    //                 int totalShots = 0;
    //                 for (int i = 0; i < showdownTargetCounts.Length; i++)
    //                 {
    //                     totalShots += showdownTargetCounts[i];

    //                     // Check if this player was targeted by all 3 shots
    //                     if (showdownTargetCounts[i] == 3 && totalShots == 3)
    //                     {
    //                         var targetPlayer = GetSeatPlayerObject(i);
    //                         if (Utilities.IsValid(targetPlayer))
    //                         {
    //                             targetPlayer.StoreContinental();
    //                         }
    //                     }
    //                 }

    //                 // Clear the target counts for next showdown
    //                 for (int i = 0; i < showdownTargetCounts.Length; i++)
    //                 {
    //                     showdownTargetCounts[i] = 0;
    //                 }

    //                 for (int i = 0; i < showdownDeaths.Length; i++)
    //                 {
    //                     if (showdownDeaths[i])
    //                     {
    //                         GetSeatPlayerObject(i).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
    //                         if (thisRoundShowdownDeaths == 4)
    //                         {
    //                             GetSeatPlayerObject(i).persistentStats.SendCustomNetworkEvent(
    //                                         VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StoreThenThereWereNone");
    //                         }
    //                         PlayerDied(i, -1);
    //                     }
    //                 }

    //                 if (_IsGameOverBecauseOnePlayerLeft())
    //                 {
    //                     return;
    //                 }
    //                 else
    //                 {
    //                     if (_IsTheGameADraw())
    //                     {
    //                         if (thisRoundShowdownDeaths == 4) // FOR TESTING, 4 FOR LIVE
    //                         {
    //                             // Debug.Log("OMG ITS A THING");
    //                             for (var i = 0; i < playerIDsArray.Length; i++)
    //                             {
    //                                 if (playerIDsArray[i] != -1)
    //                                 {
    //                                     GetSeatPlayerObject(i).persistentStats.SendCustomNetworkEvent(
    //                                         VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StoreThenThereWereNone");
    //                                 }
    //                             }

    //                             if (_IsLocalPlayerTableMaster() && IsLocalPlayerSeatedAtThisTable())
    //                             {
    //                                 _pod.GetLocalPlayerScript().persistentStats.SendCustomNetworkEvent(
    //                                         VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "StoreThenThereWereNone");
    //                             }
    //                         }
    //                         gameDraw = true;
    //                         gameStatus = 5;
    //                         RequestSerialization();
    //                         OnDeserialization();
    //                         if (!gameOverTimer)
    //                         {
    //                             gameOverTimer = true;
    //                             SendCustomEventDelayedSeconds("GameOver", 6f);
    //                         }
    //                         return;
    //                     }
    //                     else
    //                     {
    //                         _StartNextRoundAfterAShot(true);
    //                     }
    //                 }
    //             }
    //         }
    //         // Modified section of _ShotFired for QuickDraw
    //         else if (quickdrawCalled)
    //         {
    //             // Force synchronization before processing
    //             RequestSerialization();
    //             OnDeserialization();

    //             // If someone is already dead in this quickdraw, ignore further shots
    //             if (qdChallengerDead || qdChallengeTargetDead)
    //             {
    //                 return;
    //             }

    //             if (quickDrawShotsLeft <= 0)
    //             {
    //                 return;
    //             }

    //             // ================ CRITICAL SECTION - PROTECT FROM RACE CONDITIONS ================
    //             // Add a unique identifier for this shot to detect simultaneous processing
    //             // Use timestamp + random number to make it unique
    //             string shotID = Time.time.ToString() + UnityEngine.Random.Range(0, 10000);

    //             // // Already processed a shot in this frame? (Race condition detection)
    //             // if (Time.frameCount == lastQuickdrawShotFrame)
    //             // {
    //             //     Debug.Log("Detected simultaneous shots in same frame! Ignoring later one with ID: " + shotID);
    //             //     return;
    //             // }
    //             // lastQuickdrawShotFrame = Time.frameCount;

    //             // Process this shot - was it valid and did it hit someone in the duel?
    //             if (shotKilled && shotTarget != -1)
    //             {
    //                 // Check if the shot hit someone in the quickdraw
    //                 if (shotTarget == qdChallengeTarget || shotTarget == qdChallenger)
    //                 {
    //                     // CRITICAL: Check one more time if someone already died 
    //                     // (Race condition between network syncs)
    //                     if (qdChallengerDead || qdChallengeTargetDead)
    //                     {
    //                         return;
    //                     }

    //                     // This is a valid quickdraw shot that hit a target - it resolves the duel immediately
    //                     quickDrawShotsLeft = 0; // No more shots needed

    //                     if (qdAwardTenPaces)
    //                     {
    //                         GetSeatPlayerObject(qdChallenger, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
    //                         GetSeatPlayerObject(qdChallengeTarget, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
    //                         qdAwardTenPaces = false;
    //                     }

    //                     // Track appropriate stats based on who shot whom
    //                     if (shotTarget == qdChallengeTarget)
    //                     {
    //                         var plr = GetSeatPlayerObject(qdChallenger);
    //                         if (Utilities.IsValid(plr))
    //                         {
    //                             plr.StoreQDKill();
    //                         }
    //                         qdChallengeTargetDead = true;
    //                         GetSeatPlayerObject(shotTarget).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");

    //                         _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IncremengMatchQDChallengeKills");
    //                         _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IncrementQDTableWins");
    //                         PlayerDied(shotTarget, shooterID);

    //                     }
    //                     else if (shotTarget == qdChallenger)
    //                     {
    //                         var plr = GetSeatPlayerObject(qdChallengeTarget);
    //                         if (Utilities.IsValid(plr))
    //                         {
    //                             plr.StoreQDDefense();
    //                         }
    //                         qdChallengerDead = true;
    //                         GetSeatPlayerObject(shotTarget).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
    //                         _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IncrementQDTableWins");
    //                         PlayerDied(shotTarget, shooterID);

    //                     }


    //                     // Handle the player death - remove them from the game
    //                     // seatStatuses[shotTarget] = false;
    //                     // playerIDsArray[shotTarget] = -1;
    //                     // PackSeatStatuses();
    //                     // SerializePlayerIDs();

    //                     // Update game state - CRITICAL for synchronizing player death
    //                     // Synchronize BEFORE checking game over
    //                     RequestSerialization();
    //                     OnDeserialization();



    //                     // Add a small delay to allow network sync before determining game state
    //                     SendCustomEventDelayedSeconds("CheckQuickdrawGameState", 0.2f);

    //                     return; // Exit immediately after resolving
    //                 }
    //                 else
    //                 {
    //                     quickDrawShotsLeft--;

    //                     // // CRITICAL: Check one more time if someone already died 
    //                     // // (Race condition between network syncs)
    //                     // if (qdChallengerDead || qdChallengeTargetDead)
    //                     // {
    //                     //     Debug.Log("Race condition detected! Shot with ID " + shotID + " ignored because someone is already dead.");
    //                     //     return;
    //                     // }

    //                     if (quickDrawShotsLeft <= 0)
    //                     {
    //                         if (qdAwardTenPaces)
    //                         {
    //                             GetSeatPlayerObject(qdChallenger, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
    //                             GetSeatPlayerObject(qdChallengeTarget, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
    //                             qdAwardTenPaces = false;
    //                         }
    //                         // All shots used with no valid hits
    //                         // Add a small delay to allow network sync before determining game state
    //                         SendCustomEventDelayedSeconds("CheckQuickdrawGameState", 0.2f);
    //                     }
    //                     return;
    //                 }
    //             }
    //             else
    //             {
    //                 quickDrawShotsLeft--;

    //                 // CRITICAL: Check one more time if someone already died 
    //                 // (Race condition between network syncs)
    //                 if (qdChallengerDead || qdChallengeTargetDead)
    //                 {
    //                     return;
    //                 }

    //                 if (quickDrawShotsLeft == 0)
    //                 {
    //                     if (qdAwardTenPaces)
    //                     {
    //                         GetSeatPlayerObject(qdChallenger, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
    //                         GetSeatPlayerObject(qdChallengeTarget, true).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "AwardDBFlint");
    //                         qdAwardTenPaces = false;
    //                     }
    //                     // All shots used with no valid hits
    //                     // Add a small delay to allow network sync before determining game state
    //                     SendCustomEventDelayedSeconds("CheckQuickdrawGameState", 0.2f);
    //                 }
    //                 return;
    //             }
    //         }
    //         else
    //         {
    //             if (qdChallengerDead || qdChallengeTargetDead)
    //             {
    //                 Debug.Log("Ignoring regular/gunslinger shot - we just resolved a quickdraw with a death");
    //                 return;
    //             }

    //             if (shotKilled && !suicide && shotTarget == -1)
    //             {
    //                 _StartNextRoundAfterAShot(true);
    //                 return;
    //             }

    //             if (shotKilled)
    //             {
    //                 if (suicide)
    //                 {
    //                     _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shooterID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
    //                     PlayerDied(GetSeatIDForPlayer(shooterID), shooterID);

    //                 }
    //                 else
    //                 {
    //                     GetSeatPlayerObject(shotTarget).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
    //                     PlayerDied(shotTarget, shooterID);

    //                 }

    //             }

    //             if (_IsGameOverBecauseOnePlayerLeft())
    //             {
    //             }
    //             else
    //             {
    //                 _StartNextRoundAfterAShot();

    //             }


    //         }
    //     }
    // }

    public void CheckQuickdrawGameState()
    {
        Debug.Log("Checking quickdraw game state after delay");

        // Force synchronization before checking game state
        RequestSerialization();
        OnDeserialization();

        if (_IsGameOverBecauseOnePlayerLeft())
        {
            Debug.Log("Game is over after quickdraw.");
        }
        else
        {
            Debug.Log("Game continues after quickdraw. Starting new round.");
            _StartNextRoundAfterAShot(true);
        }
    }

    private string _ArrayUtility_Join_intarray_string(int[] deadPlayerIDsArray, string v)
    {
        string result = "";
        for (int i = 0; i < deadPlayerIDsArray.Length; i++)
        {
            if (deadPlayerIDsArray[i] != -1)
            {
                result += deadPlayerIDsArray[i] + ";";
            }
        }
        return result;
    }

    private int GetLastPlayerStanding()
    {
        if (currentGameMode == 1 && showdownCalled)
        {
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i] && !showdownDeaths[i])
                {
                    return i;
                }
            }
        }
        else
        {
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
                {
                    return i;
                }
            }
        }

        return -1;
    }

    public void _LogDumpGameOver()
    {
        DumpGameState();

        // Now, we need to reset absolutely everything
        for (var i = 0; i < stations.Length; i++)
        {
            stations[i].transform.parent.GetComponent<PlayerSeat>().GameEndReset();
            seatStatuses[i] = false;
            playerIDsArray[i] = -1;
        }

        if (Utilities.IsValid(localPlayerCache))
        {
            tableMasterID = localPlayerCache.playerId;
            _tableMaster = localPlayerCache;

            Networking.SetOwner(localPlayerCache, gameObject);

            RequestSerialization();
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "GameOver");
        }
    }

    public void GameOver()
    {
        Debug.Log("<color=#FF00FF>Game over!</color>");

        // Reset game state variables
        gameStatus = 0;
        currentRound = 0;
        currentRoundPlayer = -1;
        currentRoundPlayerVersion++;
        currentRoundTurn = 0;
        gameStarted = false;
        didPreviousPlayerBullshit = false;
        previousSeatId = -1;
        lastSubmittedCardsString = "";
        cardOwnership = "";
        rouletteTarget = -1;
        bRevealCards = false;
        deadPlayerIDs = "";
        deadPlayerIDsArray = new int[4] { -1, -1, -1, -1 };
        cumulativeDeaths = "";
        currentRoundRandomValue = -1;
        forcedBSCall = -1;
        gameWinDefault = false;
        lastSeatToDie = -1;
        lastRoundQuit = false;
        devilCardCalled = false;
        devilCardID = -1;
        bCorrectCallShowdown = false;
        showdownCalled = false;
        showdownShooters = 0;
        showdownGunsPickedUp = 0;
        gunslingerCalled = false;
        gameWinner = -1;
        BSCaller = -1;
        thisRoundShowdownDeaths = 0;

        devilCardshots = 0;
        devilCardshotsTaken = 0;

        // Reset local variables
        _l_chairsLocked = false;
        _l_gameStarted = false;
        _l_currentRound = 0;
        _l_currentRoundPlayer = -1;
        _l_currentRoundTurn = 0;
        _l_didPreviousPlayerBullshit = false;
        _l_previousSeatId = -1;
        _l_lastSubmittedCardsString = "";
        _l_rouletteTarget = -1;
        _l_bRevealCards = false;
        // _l_deadPlayerID = -1;
        _l_deadPlayerIDs = "";
        deadPlayerIDsArray = new int[4] { -1, -1, -1, -1 };
        _l_cardOwnership = "";
        _l_currentRoundRandomValue = -1;
        _currentGameKills = new int[4] { 0, 0, 0, 0 };
        _l_devilCardID = -1;
        _l_bCorrectCallShowdown = false;
        _l_showdownCalled = false;
        _l_gunslingerCalled = false;
        _l_showdownTriggerTime = -1;

        gameDraw = false;
        quickdrawCalled = false;
        _l_quickdrawCalled = false;

        qdDrawTime = -1;
        _l_qdDrawTime = -1;

        _l_qdChallenger = -1;
        qdAwardTenPaces = false;
        _l_qdChallengeTarget = -1;

        qdChallenger = -1;
        qdChallengeTarget = -1;

        qdChallengerDead = false;
        qdChallengeTargetDead = false;

        _l_qdChallengerDead = false;
        _l_qdChallengeTargetDead = false;



        showdownDeaths = new bool[4] { false, false, false, false };
        gunsPickedUpForShowdown = new bool[4] { false, false, false, false };

        if (_IsLocalPlayerTableMaster())
        {
            // Reset seat statuses
            for (var i = 0; i < seatStatuses.Length; i++)
            {
                seatStatuses[i] = false;
                playerIDsArray[i] = -1;
                Networking.SetOwner(localPlayerCache, stations[i].transform.parent.gameObject);
                // stations[i].transform.parent.gameObject.GetComponent<PlayerSeat>().EnableMe();
            }
            PackSeatStatuses();
            SerializePlayerIDs();

            // check for the For Whom The Bell Tolls achievement
            UnlockAllChairs();

        }

        // Reset all cards
        for (int i = 0; i < cards.Length; i++)
        {
            cards[i].GetComponent<PlayingCard>()._ResetCard();
        }

        // Disable revolver indicators
        for (int i = 0; i < revolverIndicators.Length; i++)
        {
            revolverIndicators[i].SetActive(false);
            revolverIndicators[i].GetComponent<Animator>().speed = 0;
        }

        // Disable reveal cards
        for (int i = 0; i < revealCards.Length; i++)
        {
            revealCards[i].GetComponent<PlayingCard>().DisableCardRenderers();
        }

        // Hide turn indicator
        turnIndicatorPointer.SetActive(false);

        if (_IsLocalPlayerTableMaster())
        {
            RequestSerialization();
            OnDeserialization(); // Ensure master also processes the reset
        }

        if (IsLocalPlayerSeatedAtThisTable())
            _pod.GetLocalPlayerScript().GameOver();

        // Re-enable colliders and stations
        for (int i = 0; i < stations.Length; i++)
        {
            stations[i].gameObject.GetComponent<BoxCollider>().enabled = true;
            stations[i].enabled = true;
        }

        // Reset shot texts and canvases
        for (var i = 0; i < shotsTexts.Length; i++)
        {
            shotsTexts[i].text = "0/6";
        }
        for (var i = 0; i < shotsCanvases.Length; i++)
        {
            shotsCanvases[i].SetActive(false);
        }

        // Hide deck animator card
        deckAnimator.ResetTrigger("HideCard");
        deckAnimator.SetTrigger("HideCard");
        // Debug.Log($"<color=#FF00FF>HideCard from GameOver</color>");

        // // Ensure all PlayerSeats are enabled
        // for (var i = 0; i < stations.Length; i++)
        // {
        //     stations[i].transform.parent.GetComponent<PlayerSeat>().EnableMe();
        // }

        gameOverTimer = false;
        unjammerStarted = false;
        unjammerStop = false;

        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "DisableAntiNonsenseBarrier");

        // if (_pod.GetPlayerScript(Networking.Master)._inDebugMode)
        // {
        //     // SendCustomEventDelayedSeconds("") TESTING-STUFF
        //     SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "DebugGameOver");
        // }
    }

    // public void DebugGameOver()
    // {
    //     SendCustomEventDelayedSeconds("DebugSeatStuff", 7f);
    // }

    // public void DebugSeatStuff()
    // {
    //     _pod.GetLocalPlayerScript()._DebugTakeMySeat();

    // }

    public void UnlockAllChairs()
    {
        if (_IsLocalPlayerTableMaster())
        {
            //Debug.Log("Enabling all chairs.");
            for (int i = 0; i < stations.Length; i++)
            {
                // Debug.Log("Unlocking chair " + i);
                Networking.SetOwner(localPlayerCache, stations[i].transform.parent.gameObject);
                if (stations[i] == null) continue;
                stations[i].transform.parent.GetComponent<PlayerSeat>().GameEndReset();
                stations[i].transform.parent.GetComponent<PlayerSeat>().EnableMe();
            }
        }
        else
        {
            for (int i = 0; i < stations.Length; i++)
            {
                stations[i].transform.parent.GetComponent<PlayerSeat>().GameEndReset();
            }
        }
    }

    public void LockSpecificChair(int chairID)
    {
        // Debug.Log("Locking chair " + chairID);
        stations[chairID].gameObject.GetComponent<BoxCollider>().enabled = false;
        stations[chairID].enabled = false;
    }

    public void StartNextRound()
    {
        if (localPlayerCache.displayName == "aRkker")
        {
            Debug.Log("<color=#FFA500>[StartNextRound] Called. State: gameStatus=" + gameStatus + ", currentRound=" + currentRound +
                      ", currentRoundPlayer=" + currentRoundPlayer + ", lastRoundQuit=" + lastRoundQuit + ", tableID=" + tableID + "</color>");
        }

        startNextRoundTimer = false;

        if (!_IsLocalPlayerTableMaster() || !gameStarted || gameStatus < 1 || gameStatus > 4 ||
            showdownCalled || gameWinner != -1)
        {
            if (localPlayerCache.displayName == "aRkker")
            {
                Debug.Log("<color=#FFA500>[StartNextRound] Exiting early. Conditions not met. isMaster=" + _IsLocalPlayerTableMaster() +
                          ", gameStarted=" + gameStarted + ", gameStatus=" + gameStatus + ", showdownCalled=" + showdownCalled +
                          ", gameWinner=" + gameWinner + "</color>");
            }
            return;
        }

        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "DisableAntiNonsenseBarrier");

        // Count the number of players left
        int playersLeft = 0;
        for (int i = 0; i < seatStatuses.Length; i++)
        {
            var seatPlayer = GetSeatPlayerObject(i);

            if (seatPlayer == null)
            {
                Debug.Log("Seat player is null for index " + i);
                continue;
            }
            Debug.Log("Seat status for index " + i + " is " + seatStatuses[i] + " and dead status: " + GetSeatPlayerObject(i).dead);

            if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
            {
                playersLeft++;
            }
        }
        if (localPlayerCache.displayName == "aRkker")
        {
            Debug.Log("<color=#FFA500>[StartNextRound] Players left: " + playersLeft + "</color>");
        }
        if (playersLeft <= 1)
        {
            if (localPlayerCache.displayName == "aRkker")
            {
                Debug.Log("<color=#FFA500>[StartNextRound] Not enough players left to start a new round. Lets declare winner.</color>");
            }

            // Declare the winner if only one player is left
            gameWinner = GetLastPlayerStanding();
            gameStatus = 5;
            RequestSerialization();
            if (!gameOverTimer)
            {
                gameOverTimer = true;
                SendCustomEventDelayedSeconds("GameOver", 6f);
            }
            return;
        }

        // Set the game status to indicate a new round is starting
        gameStatus = 1;

        // If the previous round ended with a quit, force re-picking the currentRoundPlayer
        if (lastRoundQuit)
        {
            if (localPlayerCache.displayName == "aRkker")
            {
                Debug.Log("<color=#FFA500>[StartNextRound] Last round ended with a quit. Forcing re-pick of currentRoundPlayer.</color>");
            }
            lastRoundQuit = false;
            currentRoundPlayer = -1;
            currentRoundPlayerVersion++;
        }
        else
        {
            if (currentRoundPlayer == -1)
            {
                if (localPlayerCache.displayName == "aRkker")
                {
                    Debug.Log("<color=#FFA500>[StartNextRound] currentRoundPlayer is -1. Checking rouletteTarget (" + rouletteTarget + "). SeatStatuses" + "</color>");
                }

                // if (rouletteTarget == -1 && deadPlayerID != -1)
                if (rouletteTarget == -1)
                {
                    currentRoundPlayer = FindNextPlayer(rouletteTarget);
                    if (localPlayerCache.displayName == "aRkker")
                    {
                        Debug.Log("<color=#FFA500>[StartNextRound] Found next player via FindNextPlayer(deadPlayerID): " + currentRoundPlayer + "</color>");
                    }
                    if (currentRoundPlayer == -1 || currentRoundPlayer == rouletteTarget)
                    {
                        if (localPlayerCache.displayName == "aRkker")
                        {
                            Debug.LogError("<color=#FFA500>[StartNextRound] Invalid next player from FindNextPlayer; falling back to random assignment.</color>");
                        }
                    }
                    currentRoundPlayerVersion++;
                }
                else
                {
                    currentRoundPlayer = (rouletteTarget != -1 && playerIDs[rouletteTarget] != -1 && seatStatuses[rouletteTarget]) ? rouletteTarget : -1;
                    if (localPlayerCache.displayName == "aRkker")
                    {
                        Debug.Log("<color=#FFA500>[StartNextRound] Set currentRoundPlayer from rouletteTarget: " + currentRoundPlayer + "</color>");
                    }
                    currentRoundPlayerVersion++;
                }
            }
            else
            {
                if (localPlayerCache.displayName == "aRkker")
                {
                    Debug.Log("<color=#FFA500>[StartNextRound] currentRoundPlayer already set: " + currentRoundPlayer + "</color>");
                }
            }
        }

        // Fallback: if currentRoundPlayer is still -1, pick one randomly from valid seats.
        if (currentRoundPlayer == -1)
        {
            if (localPlayerCache.displayName == "aRkker")
            {
                Debug.LogError("<color=#FFA500>[StartNextRound] currentRoundPlayer is still -1. Picking a random valid seat.</color>");
            }
            int[] validSeats = new int[seatStatuses.Length];
            int validCount = 0;
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i])
                {
                    Player seatPlayer = GetSeatPlayerObject(i);
                    if (seatPlayer != null && !seatPlayer.dead && !showdownDeaths[i])
                    {
                        validSeats[validCount++] = i;
                    }
                }
            }
            if (validCount == 0)
            {
                if (localPlayerCache.displayName == "aRkker")
                {
                    Debug.LogError("<color=#FFA500>[StartNextRound] No valid players found to start the next round.</color>");
                }
                return;
            }
            int randomIndex = rng.Next(validCount);
            currentRoundPlayer = validSeats[randomIndex];
            currentRoundPlayerVersion++;
            if (localPlayerCache.displayName == "aRkker")
            {
                Debug.Log("<color=#FFA500>[StartNextRound] Randomly selected currentRoundPlayer: " + currentRoundPlayer + "</color>");
            }
            RequestSerialization();
        }

        if (quickdrawCalled)
        {
            deckAudio.Stop();
        }

        // Reset round-specific variables for a fresh start
        rouletteTarget = -1;
        devilCardKillsThisRound = 0;
        gunsPickedUpForShowdown = new bool[4] { false, false, false, false };
        showdownDeaths = new bool[4] { false, false, false, false };
        deadPlayerIDs = "";
        deadPlayerIDsArray = new int[4] { -1, -1, -1, -1 };
        cumulativeDeaths = "";
        showdownCalled = false;
        bCorrectCallShowdown = false;
        gunslingerCalled = false;
        BSCaller = -1;
        didPreviousPlayerBullshit = false;
        thisRoundShowdownDeaths = 0;
        bRevealCards = false;
        _l_bRevealCards = false;
        quickdrawCalled = false;
        qdDrawTime = -1;
        qdChallenger = -1;
        qdAwardTenPaces = false;
        qdChallengeTarget = -1;
        qdChallengerDead = false;
        qdChallengeTargetDead = false;

        _l_quickdrawCalled = false;
        _l_quickdrawCardID = -1;
        _l_qdChallengerDead = false;
        _l_qdChallengeTargetDead = false;

        _l_qdChallenger = -1;
        _l_qdChallengeTarget = -1;
        _l_qdDrawTime = -1;

        devilCardshots = 0;
        devilCardshotsTaken = 0;

        if (localPlayerCache.displayName == "aRkker")
        {
            Debug.Log("<color=#FFA500>[StartNextRound] Reset round-specific variables. New currentRoundPlayer: " + currentRoundPlayer +
                      ", currentRoundPlayerVersion: " + currentRoundPlayerVersion + "</color>");
        }

        // Pull and shuffle cards, then schedule the random card value assignment.
        PullAndShuffleCards();
        if (localPlayerCache.displayName == "aRkker")
        {
            Debug.Log("<color=#FFA500>[StartNextRound] Cards pulled and shuffled. Scheduling RandomCardValueForRound in 2 seconds.</color>");
        }
        SendCustomEventDelayedSeconds("RandomCardValueForRound", 2f);
    }

    internal void PrepareNextRound()
    {
        _l_currentRoundRandomValue = -1;
        currentRound = -1;
    }

    /// <summary>
    /// Handles the player death event.
    /// </summary>
    /// <param name="deadSeat">Seat ID for the dead player</param>
    /// <param name="shooter">PlayerID for the shooer</param>
    /// <param name="bsCallerSeatID">Optional seat ID for the BS caller, if applicable</param>

    internal void PlayerDied(int deadSeat, int shooter, int bsCallerSeatID = -1)
    {
        Debug.Log("Got it, player call for death happened. Lets continue from here. Deadseat: " + deadSeat + ", shooter: " + shooter + ", bsCallerSeatID: " + bsCallerSeatID);
        // Mark the player as dead and remove them from the game.

        // Debug.Log("seatstatus for dead player: " + seatStatuses[deadSeat]);

        if (deadSeat == -1 || seatStatuses[deadSeat] == false)
        {
            Debug.LogError("Player at seat " + deadSeat + " is already dead. Ignoring the death call.");
            return;
        }

        if (!devilCardCalled)
        {
            int shootie = GetSeatIDForPlayer(shooter);
            if (didPreviousPlayerBullshit && shooter != -1 && shootie != -1 && bsCallerSeatID != -1)
                _currentGameKills[bsCallerSeatID] += 1;

            for (int i = 0; i < _currentGameKills.Length; i++)
            {
                if (_currentGameKills[i] >= 3 && currentGameMode != 1 && fullHouse)
                {
                    GetSeatPlayerObject(i).GetComponent<Player>().persistentStats.StoreForWhomTheBellTolls();
                }
            }

            Debug.Log("Current game mode: " + currentGameMode + ", shooter: " + shooter + ", deadSeat: " + deadSeat + ", showdownCalled: " + showdownCalled + ", preivousSeat: " + previousSeatId + ", currentPlayerID: " + currentRoundPlayer);

            if (currentGameMode != 1 && deadSeat != -1 && !showdownCalled)
                GetSeatPlayerObject(BSCaller).StoreKill();
        }
        deadPlayerIDsArray[deadSeat] = GetSeatPlayerObject(deadSeat).Owner.playerId;
        seatStatuses[deadSeat] = false;
        playerIDsArray[deadSeat] = -1;

        currentRoundPlayer = -1;
        currentRoundPlayerVersion++;
        LockSpecificChair(deadSeat);
        PackSeatStatuses();
        SerializePlayerIDs();
        RequestSerialization();
        if (_IsLocalPlayerTableMaster())
        {
            OnDeserialization();
        }


        //Debug.Log("DCK: " + devilCardKillsThisRound)
    }

    internal void UpdateSeatShotsText(int mySeatID, int shotsFired)
    {
        if (mySeatID == -1) return;

        // Debug.Log("Updating seat shots text for seat " + mySeatID + " to " + shotsFired);
        shotsTexts[mySeatID].text = $"{shotsFired}/6";

    }

    internal void LockAllChairs(bool forced = false)
    {
        if (forced)
        {
            for (var i = 0; i < stations.Length; i++)
            {
                stations[i].gameObject.GetComponent<BoxCollider>().enabled = false;
                stations[i].enabled = false;
            }
            return;
        }
        if (gameStatus != 5 && gameStarted)
        {
            for (var i = 0; i < stations.Length; i++)
            {
                stations[i].gameObject.GetComponent<BoxCollider>().enabled = false;
                stations[i].enabled = false;
            }
        }
        else
        {
            for (var i = 0; i < stations.Length; i++)
            {
                stations[i].gameObject.GetComponent<BoxCollider>().enabled = true;
                stations[i].enabled = true;
            }
        }
    }


    internal void _QuitPlayer(int l_mySeatID, int playerID)
    {
        // Early exit if invalid seat
        if (l_mySeatID == -1)
        {
            return;
        }



        // Check if player was actually in the game
        bool wasPlayerDeath;
        int remainingPlayers;
        int lastAliveSeat;

        bool playerInGame = false;
        for (int i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] == playerID)
            {
                playerInGame = true;
                break;
            }
        }

        if (!playerInGame)
        {
            // Handle forced kicks vs instance quits
            var player = VRCPlayerApi.GetPlayerById(playerID);
            bool isPlayerStillInInstance = (player != null && player.IsValid());

            if (isPlayerStillInInstance)
            {
                // This is a forced kick, not an instance quit
                if (gameStarted)
                {
                    Debug.Log("Disabling chair from _QuitPlayer #1");
                    stations[l_mySeatID].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();

                    // Check for winner after forced kick
                    wasPlayerDeath = (l_mySeatID == lastSeatToDie);
                    remainingPlayers = 0;
                    lastAliveSeat = -1;

                    for (int i = 0; i < seatStatuses.Length; i++)
                    {
                        if (seatStatuses[i])
                        {
                            remainingPlayers++;
                            lastAliveSeat = i;
                        }
                    }

                    if (remainingPlayers == 1 && lastAliveSeat != -1 && gameStatus != 5)
                    {
                        // Declare winner
                        gameStatus = 5;
                        gameWinner = lastAliveSeat;
                        gameWinDefault = !wasPlayerDeath;
                        Debug.Log("Game winner: " + gameWinner);
                        RequestSerialization();
                        OnDeserialization();

                        if (!gameOverTimer)
                        {
                            gameOverTimer = true;
                            SendCustomEventDelayedSeconds("GameOver", 6f);
                        }
                    }
                    else if (gameStatus != 1 && gameStatus != 5 && showdownCalled)
                    {
                        // Check showdown readiness
                        int playersLeft = 0;
                        for (int i = 0; i < seatStatuses.Length; i++)
                        {
                            if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
                            {
                                playersLeft++;
                            }
                        }

                        if (showdownGunsPickedUp == playersLeft)
                        {
                            SetupShowdownTriggerTimeout();
                        }
                    }
                }
            }
            return;
        }

        // === Handle actual player quit/death ===
        wasPlayerDeath = (l_mySeatID == lastSeatToDie);

        // Update showdown state if needed
        if (showdownCalled)
        {
            showdownShooters--;
        }

        // Remove player from game
        _ReturnPlayersCards(playerID);
        seatStatuses[l_mySeatID] = false;
        playerIDsArray[l_mySeatID] = -1;
        PackSeatStatuses();
        SerializePlayerIDs();
        RequestSerialization();

        // Only process further if game has started
        if (!gameStarted)
        {
            // Handle pre-game quit
            int playersSeated = 0;
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i]) playersSeated++;
            }

            if (gameStatus != 5 && playersSeated == 0)
            {
                UnlockAllChairs();
            }
            return;
        }

        // Disable the chair
        Debug.Log("Disabling chair from _QuitPlayer #2");
        stations[l_mySeatID].transform.parent.gameObject.GetComponent<PlayerSeat>().DisableMe();

        // Check for game winner
        remainingPlayers = 0;
        lastAliveSeat = -1;

        for (int i = 0; i < seatStatuses.Length; i++)
        {
            if (seatStatuses[i])
            {
                remainingPlayers++;
                lastAliveSeat = i;
            }
        }

        if (remainingPlayers == 1 && lastAliveSeat != -1 && gameStatus != 5)
        {
            // Declare winner
            gameStatus = 5;
            gameWinner = lastAliveSeat;
            gameWinDefault = !wasPlayerDeath;
            Debug.Log("Game winner: " + gameWinner);
            RequestSerialization();
            OnDeserialization();

            if (!gameOverTimer)
            {
                gameOverTimer = true;
                SendCustomEventDelayedSeconds("GameOver", 6f);
            }
            return;
        }

        // === Handle active game quit scenarios ===

        // Handle normal turn progression (no special calls active)
        if (gameStatus != 1 && gameStatus != 5 && !showdownCalled && !gunslingerCalled &&
            !quickdrawCalled && BSCaller == -1)
        {
            if (currentRoundPlayer == l_mySeatID)
            {
                Debug.Log("<color=#76f59a>Current player quit, passing turn</color>");
                currentRoundPlayer = FindNextPlayer(l_mySeatID);
                currentRoundPlayerVersion++;
                RequestSerialization();
                OnDeserialization();
            }
        }

        // Handle BS call scenarios
        else if (BSCaller != -1)
        {
            bool needNewRound = false;

            if (devilCardCalled || showdownCalled)
            {
                needNewRound = true;
            }
            else if (gunslingerCalled && l_mySeatID == rouletteTarget)
            {
                needNewRound = true;
            }
            else if (quickdrawCalled && (l_mySeatID == qdChallenger || l_mySeatID == qdChallengeTarget))
            {
                needNewRound = true;
            }
            else if (l_mySeatID == rouletteTarget)
            {
                // Regular BS call where quitter was the target
                needNewRound = true;
            }

            if (needNewRound && gameStatus != 5 && gameStarted)
            {
                Debug.Log("<color=#76f59a>Quitter was involved in special call, starting new round</color>");

                // Start new round after quit
                currentRound++;
                devilCardCalled = false;
                currentRoundTurn = 0;
                currentRoundPlayerVersion++;
                lastRoundQuit = true;

                currentRoundRandomValue = -1;
                didPreviousPlayerBullshit = false;
                lastSubmittedCardsString = "";
                bRevealCards = false;
                cardOwnership = "";
                _l_cardOwnership = "";
                forcedBSCall = -1;
                BSCaller = -1;
                gameStatus = 4;
                showdownCalled = false;
                bCorrectCallShowdown = false;
                gunslingerCalled = false;

                quickdrawCalled = false;
                qdDrawTime = -1;
                qdChallenger = -1;
                qdAwardTenPaces = false;
                qdChallengeTarget = -1;

                RequestSerialization();
                OnDeserialization();

                if (!startNextRoundTimer)
                {
                    Debug.Log("<color=#76f59a>StartNextRound from quit handler</color>");
                    startNextRoundTimer = true;
                    SendCustomEventDelayedSeconds("StartNextRound", 3f);
                }
                return;
            }
        }

        // Handle showdown
        if (gameStatus != 1 && gameStatus != 5 && showdownCalled)
        {
            // Check showdown readiness
            int playersLeft = 0;
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
                {
                    playersLeft++;
                }
            }

            if (showdownGunsPickedUp == playersLeft)
            {
                SetupShowdownTriggerTimeout();
            }
        }

        // Handle quickdraw
        if (gameStatus != 1 && gameStatus != 5 && quickdrawCalled)
        {
            if ((qdChallenger == l_mySeatID || qdChallengeTarget == l_mySeatID) &&
                _IsLocalPlayerTableMaster())
            {
                deckAudio.Stop();
                _StartNextRoundAfterAShot(true);
            }
        }

        // IMPORTANT: Final check - if quit player was current round player in normal game
        if (l_mySeatID == currentRoundPlayer && gameStarted && gameStatus == 3)
        {
            Debug.Log("<color=#76f59a>Quit player was current round player, advancing turn</color>");
            AdvanceToNextPlayer();
        }
    }

    internal void PlayTurnSound()
    {
        deckAudio.PlayOneShot(oneShotClips[2]);
    }

    internal void ChangeIndicatorToMyColour()
    {
        //Debug.Log("My turn");
        turnIndicatorPointer.GetComponent<MeshRenderer>().material = myTurnMaterial;
    }

    internal void ChangeIndicatorToNotMyColour()
    {
        //Debug.Log("Not my turn.");
        turnIndicatorPointer.GetComponent<MeshRenderer>().material = notMyTurnMaterial;
    }

    internal void HideStartButton()
    {
        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "HideStartButtonNetworked");
    }

    public void HideStartButtonNetworked()
    {
        if (IsLocalPlayerSeatedAtThisTable())
        {
            if ((startAllowed - GetNormalizedServerTime()) / 1000 > 0)
            {
                startButton.SetActive(false);

                Debug.Log("Showing start button in " + (startAllowed - GetNormalizedServerTime()) / 1000 + " seconds.");
                SendCustomEventDelayedSeconds("EnableStartButton", (float)((startAllowed - GetNormalizedServerTime()) / 1000));
            }
            else
            {
                EnableStartButton();
            }
        }
    }

    internal void UpdateSeatGunPickedUpForShowdown(int mySeatID, bool gunPickedUpForShowdown)
    {
        // Debug.Log("Seat " + mySeatID + " has picked up their gun for showdown: " + gunPickedUpForShowdown + "shodown called: " + showdownCalled);
        if (gameStarted && showdownCalled && mySeatID != -1)
        {
            // Find how many players are still playing and alive, so we know how many to wait for
            int playersLeft = 0;
            for (int i = 0; i < seatStatuses.Length; i++)
            {
                if (seatStatuses[i] && !GetSeatPlayerObject(i).dead)
                {
                    playersLeft++;
                }
            }

            // Debug.Log("We have a total of " + playersLeft + " players left. This guy has their gun picked up: " + gunsPickedUpForShowdown[mySeatID]);

            if (!gunsPickedUpForShowdown[mySeatID])
            {
                gunsPickedUpForShowdown[mySeatID] = gunPickedUpForShowdown;
                showdownGunsPickedUp++;

                if (showdownGunsPickedUp == playersLeft)
                {
                    // Debug.Log("All players have picked up their guns. We can now start the showdown.");
                    SetupShowdownTriggerTimeout();
                }

            }

        }
    }

    private void SetupShowdownTriggerTimeout()
    {
        // in 7 seconds, we pull the trigger. Lets calculate the server time stamp, and serialize it

        showdownTriggerTime = GetNormalizedServerTime() + 7000;
        RequestSerialization();

        if (_IsLocalPlayerTableMaster())
        {
            OnDeserialization();
        }
    }

    public void SwitchToClassicMode()
    {
        // Debug.Log("CLASS MODE CLICK LOL");
        if (!_IsLocalPlayerTableMaster())
        {
            // Debug.Log("I am not the master. I am sending the request to the owner.");
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "SwitchToClassicMode");
            settingsAndStart.GetComponent<TableSettings>().EnableClassicMode();
            return;
        }
        else
        {
            // Debug.Log("I am the master. I am toggling the devil card mode.");
            currentGameMode = 0;
            RequestSerialization();
            OnDeserialization();

        }
    }

    public void SwitchToShowdownMode()
    {
        if (!_IsLocalPlayerTableMaster())
        {
            // Debug.Log("I am not the master. I am sending the request to the owner.");
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "SwitchToShowdownMode");
            settingsAndStart.GetComponent<TableSettings>().EnableShowdownMode();

            return;
        }
        else
        {
            // Debug.Log("I am the master. I am toggling the devil card mode.");
            currentGameMode = 1;
            RequestSerialization();
            OnDeserialization();
        }
    }

    public int[] _ArrayUtility_Add_intarray_int(int[] _0, int _1)
    {
        var result = new int[_0.Length + 1];
        _0.CopyTo(result, 0);
        result[_0.Length] = _1;
        return result;
    }

    [NetworkCallable(maxEventsPerSecond: 1)]
    public void SetQDChallengeTarget(int challengerSeatID, int ourQDChallengeTarget)
    {
        if (!NetworkCalling.InNetworkCall || NetworkCalling.CallingPlayer.playerId != GetPlayerIDForSeat(previousSeatId))
        {
            Debug.LogError("Not the QD challenger. Ignoring. qdChallenger: " + qdChallenger + ", NetworkCalling.CallingPlayer.playerId: " + NetworkCalling.CallingPlayer.playerId + ", previousSeatId: " + previousSeatId);
            return;
        }
        if (challengerSeatID == -1 || ourQDChallengeTarget == -1) return;
        Debug.Log("Seat ID " + challengerSeatID + " is the target of the QD challenge. Target: " + ourQDChallengeTarget);

        qdChallengeTarget = ourQDChallengeTarget;
        qdChallenger = challengerSeatID;

        // We need to randomize a value for when the QD actually starts. 4 to 10 seconds, a float is fine
        qdDrawTime = GetNormalizedServerTime() + (rng.Next(6, 13) * 1000);

        GetSeatPlayerObject(qdChallenger).GetComponent<Player>().SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IAmChallengingForQD", GetSeatPlayerObject(qdChallengeTarget).Owner.displayName, qdDrawTime);
        GetSeatPlayerObject(qdChallengeTarget).GetComponent<Player>().SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "IAmChallengedForQD", GetSeatPlayerObject(qdChallenger).Owner.displayName, qdDrawTime);

        GetSeatPlayerObject(qdChallenger).FindGunReference();
        GetSeatPlayerObject(qdChallengeTarget).FindGunReference();

        if (GetSeatPlayerObject(qdChallenger).myGunChoice == 8 &&
            GetSeatPlayerObject(qdChallengeTarget).myGunChoice == 8)
        {
            qdAwardTenPaces = true;
        }

        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "ToggleAntiNonsenseBarrier", qdChallenger, qdChallengeTarget);

        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "PlayQDTumbleweeds");



        RequestSerialization();
        if (_IsLocalPlayerTableMaster())
        {
            OnDeserialization();
        }
    }

    public void DisableAntiNonsenseBarrier()
    {
        if (QDAntiNonsenseHitbox.activeSelf)
        {
            QDAntiNonsenseHitbox.SetActive(false);
            Debug.Log("Anti-nonsense barrier disabled.");
        }
    }

    [NetworkCallable(maxEventsPerSecond: 4)]
    public void ToggleAntiNonsenseBarrier(int challenger, int challengeTarget)
    {
        // 1) Check if local player is involved in QD (i.e. challenger or target)
        Debug.Log("ToggleAntiNonsenseBarrier called. Challenger: " + challenger + ", ChallengeTarget: " + challengeTarget);
        int mySeat = _pod.GetLocalPlayerScript().mySeatID;
        bool localIsQD = (mySeat == challenger || mySeat == challengeTarget) && _pod.GetLocalPlayerScript().myTableID == tableID;

        if (localIsQD)
        {
            // Do nothing for QD participants.
            return;
        }

        // 2) We're NOT in QD, so enable the barrier (if not already enabled).
        if (!QDAntiNonsenseHitbox.activeSelf)
        {
            QDAntiNonsenseHitbox.SetActive(true);

            // 3) Right after enabling, check “am I inside?” and, if so, nudge myself out.
            VRCPlayerApi local = Networking.LocalPlayer;
            if (local != null)
            {
                // Assume the barrier has a BoxCollider component
                BoxCollider bc = QDAntiNonsenseHitbox.GetComponent<BoxCollider>();
                if (bc != null)
                {
                    Vector3 playerPos = local.GetPosition();
                    Vector3 closestPoint = bc.ClosestPoint(playerPos);

                    // If the player is inside the collider, closestPoint ≃ playerPos
                    if (Vector3.Distance(closestPoint, playerPos) < 0.001f)
                    {
                        // Pick a random child from the transforms 
                        Transform randomPoint = QDMoveSpots.GetChild(rng.Next(QDMoveSpots.childCount));
                        local.TeleportTo(randomPoint.position, randomPoint.rotation);
                    }
                }
            }
        }
        // If the barrier was already active, we leave it active (and do NOT disable it here).
    }


    [NetworkCallable(maxEventsPerSecond: 1)]
    public void PlayQDTumbleweeds()
    {
        tumbleWeedObject.SetActive(true);
    }

    public void PlayFireSound()
    {
        Debug.Log("FIRE SOUND!");
        if (gameStarted && gameStatus != 5 && quickdrawCalled)
        {
            deckAudio.Stop();
            qdAudio.Stop();
            qdAudio.PlayOneShot(oneShotClips[11]);
        }
    }

    internal bool _IsLocalPlayerPlaying()
    {
        for (var i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] == localPlayerCache.playerId)
            {
                return true;
            }
        }

        return false;
    }

    internal int GetPlayerIDForSeat(int ourShotTarget)
    {
        Debug.Log("Getting player ID for seat " + ourShotTarget);
        if (ourShotTarget == -1)
        {
            Debug.LogError("Seat ID is -1. Returning -1.");
            return -1;
        }
        return playerIDsArray[ourShotTarget];
    }

    internal int NumberOfPeopleWithCards()
    {
        int peopleWithCards = 0;

        for (var i = 0; i < playerIDsArray.Length; i++)
        {
            if (playerIDsArray[i] != -1 && seatStatuses[i])
            {
                if (GetSeatPlayerObject(i).cardsLeftInHand > 0)
                {
                    peopleWithCards++;
                }
            }
        }

        return peopleWithCards;
    }

    internal void _SetQDChallengeText(string displayName, bool challenger)
    {
        var trackingData = Networking.LocalPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);
        // the offset you originally used for a 1.80 m avatar:
        const float defaultHeight = 1.80f;
        const float defaultOffset = 0.5f;

        float avatarHeight = Networking.LocalPlayer.GetAvatarEyeHeightAsMeters();

        // compute a scaled offset
        float scaledOffset = defaultOffset * (avatarHeight / defaultHeight);

        // throw new NotImplementedException();
        if (challenger)
        {
            // grab the head‐tracking data once

            // combine: start at head position, push forward, then push down
            QDChallengerText.transform.position = new Vector3(QDChallengerText.transform.position.x, trackingData.position.y - scaledOffset, QDChallengerText.transform.position.z);
            QDChallengerText.SetActive(true);

            QDChallengerText.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = "You have challenged <color=\"red\">" + displayName + "</color> to a Quickdraw!\nPrepare yourself, and wait for the signal!";
        }
        else
        {

            QDChallengeTargetText.transform.position = new Vector3(QDChallengeTargetText.transform.position.x, trackingData.position.y - scaledOffset, QDChallengeTargetText.transform.position.z);


            QDChallengeTargetText.SetActive(true);
            QDChallengeTargetText.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = "You have been challenged to a Quickdraw by <color=\"red\">" + displayName + "</color>\nPrepare yourself, and wait for the signal!";
        }
    }

    internal void _ShowQDFirePrompt(bool challenger)
    {
        if (challenger)
        {
            QDChallengerText.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = "<color=\"red\">FIRE!</color>";
        }
        else
        {
            QDChallengeTargetText.transform.GetChild(0).GetComponent<TextMeshProUGUI>().text = "<color=\"red\">FIRE!</color>";
        }
    }

    internal void _HideQDInstructions()
    {
        QDChallengeTargetText.SetActive(false);
        QDChallengerText.SetActive(false);

    }
}
