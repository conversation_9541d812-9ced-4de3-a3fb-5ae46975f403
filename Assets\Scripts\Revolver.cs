
using System;
using TMPro;
using UdonSharp;
using Unity.Mathematics;
using UnityEngine;
using VRC.SDK3.Components;
using VRC.SDK3.Persistence;
using VRC.SDK3.UdonNetworkCalling;
using VRC.SDKBase;
using VRC.Udon;

[UdonBehaviourSyncMode(BehaviourSyncMode.Manual)]
public class Revolver : UdonSharpBehaviour
{
    [UdonSynced] public bool shotFired = false;
    private bool _l_shotFired = false;
    [UdonSynced] public bool nextShotWillKill = false;

    public bool flyingToAutoFirePoint = false;
    private bool flyingBackToStart = false;

    [SerializeField] private Transform autoFirePoint;

    [SerializeField] private Transform desktopGunGrip;

    private Vector3 startingPosition;
    private Quaternion startingRotation;

    private Vector3 desktopAutoFirePosition;
    private Quaternion desktopAutoFireRotation;
    [UdonSynced] public Vector3 qdPosition = Vector3.zero;
    [SerializeField] private AudioSource myAudioSource;
    [SerializeField] private AudioClip[] clips = new AudioClip[2]; // [0] = empty, [1] = shot


    [SerializeField] public GameObject indicator;
    [SerializeField] public GameObject selfShotIndicator;
    [SerializeField] public GameObject otherShotIndicator;

    [UdonSynced] public bool forceFireActivated = false;
    private bool _l_forceFireActivated = false;
    [UdonSynced] public Quaternion qdRotation = Quaternion.identity;
    private GameManager myGame;
    private PlayerObjectDealer _pod;

    [SerializeField] private ParticleSystem shotParticles;
    private Animator shotAnimatorExtra;
    [SerializeField] private GameObject[] availableCustomizations = new GameObject[2];
    [SerializeField] private Animator shotAnimator;
    private AudioSource shotSoundOverride;
    [SerializeField] private AudioSource ambienceAudio;
    private GhostPhotoCapture ghostPhotoCapture;
    private Animator shotVisualAnimator;
    private LineRenderer shotVisualLineRenderer;
    private bool bringingBackAudio = false;



    [SerializeField] private AudioClip[] customizationShotClips = new AudioClip[3];
    [SerializeField] private Mesh[] meshes = new Mesh[3];
    [SerializeField] private Material[] materials = new Material[3];

    [SerializeField] private Material[] gildedSkinMaterials = new Material[3];

    [SerializeField] private Transform gildedParticleParent;


    [SerializeField] private MeshRenderer meshRenderer;
    [UdonSynced] public int customizationId = 0;
    [UdonSynced] public int customizationSkinId = 0;
    private int _l_customizationSkinId = 0;
    private int _l_customizationId = 0;

    [UdonSynced] public int particleCustomizationId = 0;
    private int _l_particleCustomizationId = 0;

    [UdonSynced] public Vector3 syncPosition = Vector3.zero;
    [UdonSynced] public Quaternion syncRotation = Quaternion.identity;

    private Vector3 _l_syncPosition = Vector3.zero;
    private Quaternion _l_syncRotation = Quaternion.identity;


    [SerializeField] private Transform muzzleTransform;
    [SerializeField] private Transform gunGripTransform;
    [SerializeField] private Transform otherGunGrip;
    [SerializeField] private GameObject crosshairCanvas;
    [SerializeField] private TextMeshProUGUI crosshairTargetName;

    [SerializeField] private Transform gunTransform; // The gun’s main transform
    [SerializeField] private Transform[] playerSeats; // Seat positions (including self)

    [SerializeField] private string[] names = new string[4] { "", "", "", "" };

    // --- Shooting temporary variables class-scoped ---
    [SerializeField] private float maxAngleDegrees = 60f;
    [SerializeField] private bool allowBehind = true;
    [SerializeField] private float minDot = 0.25f;

    // --- Reused Variables (to avoid local allocations) ---
    private float _bestScore;
    private int _bestIndex;

    private Vector3 _gunForward;
    private Vector3 _gunPosition;

    private Vector3 _seatDirection;
    private float _distance;
    private float _dot;
    private float _angle;
    private float _angleScore;
    private float _distanceWeight;
    private float _combinedScore;

    private int _i; // We'll reuse this for our loop index

    public bool isSelfShot = true;
    [UdonSynced] public bool shotMyself = true;
    [UdonSynced] private bool showDownActive = false;
    private bool gunslingerActive = false;
    public bool aimingLoopEnabled = false;

    private int localSeatIndex = 0;
    private float headDistance;
    [UdonSynced] public int ourShotTarget = -1;
    private int _l_ourShotTarget = -1;

    [SerializeField] AudioClip chamberSpinClip;

    [UdonSynced] private int shotsSerialized = 0;
    private int _l_shotsSerialized = 0;

    private float vrHeadDistance = 0.3f;
    private float pcHeadDistance = 0.4f;
    [UdonSynced, SerializeField] private bool flyingToQDPosition = false;

    [SerializeField] private Transform QDTransform;
    [SerializeField] private Transform QDTransformR;
    [SerializeField] private Transform QDTransformL;

    [SerializeField] private bool waitingForQDShot = false;

    [UdonSynced] private double QDShotTime = -1.0;

    [SerializeField] private GameObject shotVisual;

    private float moveSpeed = 0.5f;
    private float rotationSpeed = 90f;
    private float arrivalDistanceSqr = 0.0001f; // 0.01f squared
    private float arrivalAngle = 1f;

    void Start()
    {
        startingPosition = transform.position;
        startingRotation = transform.rotation;

        desktopAutoFirePosition = autoFirePoint.position;
        desktopAutoFireRotation = autoFirePoint.rotation;

        qdPosition = QDTransform.position;
        qdRotation = QDTransform.rotation;

        myGame = transform.parent.transform.parent.GetComponent<GameManager>();
        _pod = GameObject.Find("PlayerObjectDealer").GetComponent<PlayerObjectDealer>();

        ambienceAudio = GameObject.Find("AmbienceSpeaker").GetComponent<AudioSource>();
        ghostPhotoCapture = GameObject.Find("Ghoster").GetComponent<GhostPhotoCapture>();

        shotVisualAnimator = shotVisual.GetComponent<Animator>();
        shotVisualLineRenderer = shotVisual.GetComponent<LineRenderer>();

    }

    private void _ChangeCustomization(int customGunID, int skinId)
    {
        // Debug.Log("Changing customization to ID: " + customGunID + " with skin ID: " + skinId);
        meshRenderer.GetComponent<MeshFilter>().mesh = meshes[customGunID];
        Material[] matArray = materials;

        switch (skinId)
        {
            case 0:
                matArray = materials;
                if (gildedParticleParent != null)
                {
                    gildedParticleParent.gameObject.SetActive(false);
                }
                break;
            case 1:
                matArray = gildedSkinMaterials;
                if (gildedParticleParent != null)
                {
                    // Enable the gilded particle parent
                    gildedParticleParent.gameObject.SetActive(true);
                }
                else
                {
                    Debug.LogWarning("Gilded particle parent is not assigned.");
                }
                break;
        }

        meshRenderer.sharedMaterial = matArray[customGunID];
        // myAudioSource.clip = customizationShotClips[customGunID];
        clips[1] = customizationShotClips[customGunID];
        customizationId = customGunID;
        customizationSkinId = skinId;

        if (skinId == 1)
        {
            if (gildedParticleParent != null)
            {
                // Enable the gilded particle parent
                gildedParticleParent.gameObject.GetComponent<ParticleSystem>().Stop();
                var shape = gildedParticleParent.gameObject.GetComponent<ParticleSystem>().shape;
                shape.mesh = meshes[customGunID];
                gildedParticleParent.gameObject.GetComponent<ParticleSystem>().Play();

                Debug.Log("Set gilded particle mesh to: " + meshes[customGunID].name);

            }
        }
    }

    [NetworkCallable(maxEventsPerSecond: 2)]
    public void ChangeCustomization(int customGunID, int skinId)
    {
        // Debug.Log("Revolver here. Changing to customization ID: " + customGunID);
        meshRenderer.GetComponent<MeshFilter>().mesh = meshes[customGunID];
        Material[] matArray = materials;

        switch (skinId)
        {
            case 0:
                matArray = materials;
                if (gildedParticleParent != null)
                {
                    gildedParticleParent.gameObject.SetActive(false);
                }
                break;
            case 1:
                matArray = gildedSkinMaterials;
                if (gildedParticleParent != null)
                {
                    // Enable the gilded particle parent
                    gildedParticleParent.gameObject.SetActive(true);
                }
                else
                {
                    Debug.LogWarning("Gilded particle parent is not assigned.");
                }
                break;
        }

        meshRenderer.sharedMaterial = matArray[customGunID];
        // myAudioSource.clip = customizationShotClips[customGunID];
        clips[1] = customizationShotClips[customGunID];
        customizationId = customGunID;
        customizationSkinId = skinId;

        if (skinId == 1)
        {
            if (gildedParticleParent != null)
            {
                // Enable the gilded particle parent
                ParticleSystem ps = gildedParticleParent.GetComponent<ParticleSystem>();

                // Stop and clear
                ps.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);

                // Get the shape module
                var shape = ps.shape;
                shape.enabled = false;  // Disable first
                shape.mesh = meshes[customGunID];
                shape.enabled = true;   // Re-enable

                // Force refresh
                ps.Simulate(0, true, true, false);
                ps.Play();

            }
        }
        if (Networking.IsOwner(gameObject))
            RequestSerialization();
    }

    public void ChangeParticleCustomization(int customParticleID)
    {
        if (customParticleID == -1 ||
            customParticleID >= availableCustomizations.Length ||
            availableCustomizations[customParticleID] == null)
        {
            Debug.LogWarning("Invalid particle customization ID: " + customParticleID);
            return;
        }
        for (int i = 0; i < availableCustomizations.Length; i++)
        {
            if (availableCustomizations[i] != null)
            {
                // Enable only the selected effect, disable all others
                availableCustomizations[i].gameObject.SetActive(i == customParticleID);
            }
        }
        ParticleSystem ps = null;

        if (availableCustomizations[customParticleID] != null)
        {
            if (availableCustomizations[customParticleID].GetComponent<ParticleSystem>() != null)
            {
                ps = availableCustomizations[customParticleID].GetComponent<ParticleSystem>();
            }
            else
            {
                ps = availableCustomizations[customParticleID].GetComponentsInChildren<ParticleSystem>()[0];
            }
        }

        Animator pa = null;

        if (availableCustomizations[customParticleID] != null)
        {
            if (availableCustomizations[customParticleID].GetComponent<Animator>() != null)
            {
                pa = availableCustomizations[customParticleID].GetComponent<Animator>();
            }
        }

        AudioSource cas = null;

        if (availableCustomizations[customParticleID] != null)
        {
            if (availableCustomizations[customParticleID].GetComponent<AudioSource>() != null)
            {
                cas = availableCustomizations[customParticleID].GetComponent<AudioSource>();
            }
        }

        shotParticles = ps;
        shotAnimatorExtra = pa;
        shotSoundOverride = cas;
        particleCustomizationId = customParticleID;
        if (Networking.IsOwner(gameObject))
        {
            RequestSerialization();
        }
    }

    public void AssignLocalPlayer()
    {
        Networking.SetOwner(Networking.LocalPlayer, gameObject);

        if (!Networking.LocalPlayer.IsUserInVR())
        {
            GetComponent<VRCPickup>().ExactGun = desktopGunGrip;
            GetComponent<VRCPickup>().AutoHold = VRC_Pickup.AutoHoldMode.Yes;
        }

        nextShotWillKill = false;
        transform.position = startingPosition;
        transform.rotation = startingRotation;

        syncPosition = startingPosition;
        syncRotation = startingRotation;

        localSeatIndex = _pod.GetLocalPlayerScript().mySeatID;

        headDistance = Networking.LocalPlayer.IsUserInVR() ? vrHeadDistance : pcHeadDistance;


        if (PlayerData.GetBool(Networking.LocalPlayer, "BS-LEFTIE"))
        {
            Debug.Log("Leftie detected, flipping gun");
            qdPosition = QDTransformL.position;
            qdRotation = QDTransformL.rotation;
        }
        else
        {
            Debug.Log("Rightie detected, flipping gun");
            qdPosition = QDTransformR.position;
            qdRotation = QDTransformR.rotation;
        }

        waitingForQDShot = false;

        RequestSerialization();

    }

    public void PrepareGun(bool selfShot = true, bool isShowdown = false, bool isGunslinger = false)
    {
        // Debug.Log("Prep gun, selfshot: " + selfShot);
        shotFired = false;
        _l_shotFired = false;
        isSelfShot = selfShot;
        if (selfShot)
        {
            selfShotIndicator.SetActive(true);
            otherShotIndicator.SetActive(false);
        }
        else
        {
            selfShotIndicator.SetActive(false);
            otherShotIndicator.SetActive(true);
        }
        showDownActive = isShowdown;
        gunslingerActive = isGunslinger;
        if (!isSelfShot)
        {
            if (!Networking.LocalPlayer.IsUserInVR())
            {
                GetComponent<VRC_Pickup>().orientation = VRC_Pickup.PickupOrientation.Gun;
                GetComponent<VRC_Pickup>().ExactGun = otherGunGrip;
            }
            // else
            // {
            //     GetComponent<VRC_Pickup>().ExactGun = otherGunGrip;
            // }
            // crosshairCanvas.SetActive(true);

            if (Utilities.IsValid(myGame) && myGame.quickdrawCalled)
            {
                crosshairCanvas.SetActive(false);

            }
            crosshairTargetName.text = "";
            for (var i = 0; i < playerSeats.Length; i++)
            {
                if (!myGame.seatStatuses[i])
                {
                    names[i] = "";
                    continue;
                }
                names[i] = Networking.GetOwner(playerSeats[i].gameObject).displayName;
                // Debug.Log("Names: " + names[i]);
                if (Networking.GetOwner(playerSeats[i].gameObject) == Networking.LocalPlayer)
                {
                    localSeatIndex = i;
                    names[i] = "Yourself";
                }
            }
        }
        else
        {

            // gunGripTransform.rotation = Quaternion.Euler(gunGripTransform.transform.rotation.x, gunGripTransform.transform.rotation.y, -90f);
            if (!Networking.LocalPlayer.IsUserInVR())
            {
                GetComponent<VRC_Pickup>().ExactGun = desktopGunGrip;
            }
            else
            {
                GetComponent<VRC_Pickup>().ExactGun = gunGripTransform;
            }
            crosshairCanvas.SetActive(false);

        }
        GetComponent<Collider>().enabled = true;
        GetComponent<VRC_Pickup>().pickupable = true;


        RequestSerialization();
    }

    // private int GetBestTargetIndex()
    // {
    //     _bestScore = -999f;
    //     _bestIndex = -1;

    //     _gunForward = gunTransform.forward;
    //     _gunPosition = gunTransform.position;

    //     // Go through each seat
    //     for (_i = 0; _i < playerSeats.Length; _i++)
    //     {
    //         _seatDirection = (playerSeats[_i].position - _gunPosition).normalized;
    //         _dot = Vector3.Dot(_gunForward, _seatDirection);

    //         // Skip if behind and not local seat, etc.
    //         if (_i != localSeatIndex && _dot < 0f)
    //         {
    //             continue;
    //         }

    //         if (_dot > _bestScore)
    //         {
    //             _bestScore = _dot;
    //             _bestIndex = _i;
    //         }
    //     }

    //     // Optional final check: if best seat is not your seat, 
    //     // and the best dot is under some threshold, we “shoot nothing”.
    //     if (_bestIndex != -1 &&
    //         _bestIndex != localSeatIndex &&
    //         _bestScore < minDot)
    //     {
    //         _bestIndex = -1; // “No valid seat to shoot”
    //     }

    //     if (myGame.seatStatuses[_bestIndex] == false)
    //     {
    //         _bestIndex = -1;
    //     }

    //     Debug.Log("Returning best index: " + _bestIndex);

    //     return _bestIndex;
    // }


    private int GetBestTargetIndex()
    {
        // Reset best
        _bestScore = -999999f;
        _bestIndex = -1;

        // Gun info
        _gunForward = gunTransform.forward;
        _gunPosition = gunTransform.position;

        if (Vector3.Distance(Networking.LocalPlayer.GetBonePosition(HumanBodyBones.Head), gunTransform.position) < headDistance)
        {
            // Debug.Log("Probably aiming at our own head");
            return localSeatIndex;
        }

        // Loop over each seat
        for (_i = 0; _i < playerSeats.Length; _i++)
        {
            // Direction to seat
            _seatDirection = playerSeats[_i].position - _gunPosition;
            _distance = _seatDirection.magnitude;

            // Is this the local player's seat?
            bool isLocalPlayerSeat = (_i == localSeatIndex);

            // If it's the local seat, we can ignore the behind check or angle check altogether
            // or at least soften them drastically.
            if (!isLocalPlayerSeat)
            {
                // Normal seat logic
                _dot = Vector3.Dot(_gunForward, _seatDirection.normalized);

                // If not allowing behind, skip if dot < 0
                if (!allowBehind && _dot < 0f)
                {
                    continue;
                }

                // Angle check
                _angle = Vector3.Angle(_gunForward, _seatDirection);
                if (_angle > maxAngleDegrees)
                {
                    continue;
                }
            }
            else
            {
                // Local player's seat logic:
                // Maybe we allow a full 360 degrees, or a big angle to ensure it always can be targeted
                // Or skip these checks entirely.

                // Example: skip the dot < 0 check, or do a 180-degree angle check
                _angle = Vector3.Angle(_gunForward, _seatDirection);
                if (_angle > 180f)
                {
                    // Realistically, if the seat is *exactly* on the other side, it might fail,
                    // but 180 is as wide as it gets.
                    continue;
                }
            }

            // Scoring system
            // Angle-based + distance-based (you can keep or remove distance weighting as you like)
            // We must *compute* or re-compute these if we jumped over local seat logic
            _angleScore = 1f - (_angle / maxAngleDegrees);

            // If local seat, we can ensure a minimum angleScore so it doesn’t become zero
            // if we wanted to always prefer self if it’s even remotely close, for example:
            if (isLocalPlayerSeat && _angleScore < 0.1f)
            {
                _angleScore = 0.1f; // or whatever
            }

            _distanceWeight = 1f / (_distance + 1f);
            _combinedScore = _angleScore * _distanceWeight;

            // Update best seat
            if (_combinedScore > _bestScore)
            {
                _bestScore = _combinedScore;
                _bestIndex = _i;
            }
        }

        if (_bestIndex != -1 && myGame.seatStatuses[_bestIndex] == false)
        {
            _bestIndex = -1;
        }

        // Debug.Log("Returning best index: " + _bestIndex);

        return _bestIndex;
    }




    public void CleanGun()
    {
        shotFired = false;
        _l_shotFired = false;
        nextShotWillKill = false;
        GetComponent<Collider>().enabled = false;
    }

    // public override void Interact()
    // {
    //     Networking.SetOwner(Networking.LocalPlayer, gameObject);
    //     // base.Interact();
    //     if (!Networking.LocalPlayer.IsUserInVR())
    //     {
    //         // Shoot the shot!
    //         _PullTheTrigger(false);
    //         indicator.SetActive(false);
    //         forceFireActivated = true;
    //         RequestSerialization();
    //     }
    // }

    public override void OnPickupUseDown()
    {

        // _PullTheTrigger(Networking.LocalPlayer.IsUserInVR());
        if (!shotFired && !showDownActive)
        {
            if (gunslingerActive)
            {
                FireShotScheduled();
            }
            else
                Shoot();
        }
        else
        {
            // Debug.Log("shotfired, showdownactive, gunslingeractive: " + shotFired + " " + showDownActive + " " + gunslingerActive);
        }

    }

    public override void OnPickup()
    {
        // base.OnPickup();
        indicator.SetActive(false);

        flyingBackToStart = false;

        if (!isSelfShot && !myGame.quickdrawCalled)
        {
            crosshairCanvas.SetActive(true);
            aimingLoopEnabled = true;

        }
        else
        {
            crosshairCanvas.SetActive(false);
        }

        if (showDownActive)
        {
            // We need to inform the owner player object that we picked up our gun
            _pod.GetLocalPlayerScript().GunPickedUpForShowdown();
            aimingLoopEnabled = true;
            isSelfShot = false;
            _AimingLoop();
        }
        if (gunslingerActive)
        {
            _pod.GetLocalPlayerScript().GunPickedUpForSoloShot();
            aimingLoopEnabled = true;
            isSelfShot = false;

            _AimingLoop();
        }
    }

    public void _AimingLoop()
    {
        if (isSelfShot || !aimingLoopEnabled)
        {
            Debug.Log("Quitting aim loop. Bools: " + isSelfShot + " " + aimingLoopEnabled);
            return;
        }

        int targetIndex = GetBestTargetIndex();

        crosshairTargetName.text = targetIndex == -1 ? "" : names[targetIndex];

        SendCustomEventDelayedFrames("_AimingLoop", 2);
    }

    private void _PullTheTrigger(bool voluntary)
    {
        if (!voluntary)
        {
            // Debug.Log("Timeout, or some other reasno, we are forcing the shot");
            // flyingToAutoFirePoint = true;
            SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "SetFlightOn");
        }
    }

    public void SetFlightOn()
    {
        flyingToAutoFirePoint = true;
    }

    public void _PrepareGunForQD(bool challenged = false)
    {
        // flyingToQDPosition = true;

        var trackingData = Networking.LocalPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);

        flyingBackToStart = false;
        flyingToAutoFirePoint = false;

        if (!challenged && myGame.quickdrawCalled)
        {
            if (PlayerData.GetBool(Networking.LocalPlayer, "BS-LEFTIE"))
            {
                transform.position = myGame.QDChallengerGunTransformL.position;
                transform.rotation = myGame.QDChallengerGunTransformL.rotation;
            }
            else
            {
                transform.position = myGame.QDChallengerGunTransformR.position;
                transform.rotation = myGame.QDChallengerGunTransformR.rotation;
            }
        }
        else if (challenged && myGame.quickdrawCalled)
        {
            if (PlayerData.GetBool(Networking.LocalPlayer, "BS-LEFTIE"))
            {
                transform.position = myGame.QDChallengeTargetGunTransformL.position;
                transform.rotation = myGame.QDChallengeTargetGunTransformL.rotation;
            }
            else
            {
                transform.position = myGame.QDChallengeTargetGunTransformR.position;
                transform.rotation = myGame.QDChallengeTargetGunTransformR.rotation;
            }
        }

        // transform.position = new Vector3(transform.position.x, trackingData.position.y - 1.5f, transform.position.z);
        SendCustomEventDelayedFrames("DoDelayedPositionAdjustment", 2);


        nextShotWillKill = true;
        SpinTheChamber();

    }

    public void DoDelayedPositionAdjustment()
    {
        var trackingData = Networking.LocalPlayer.GetTrackingData(VRCPlayerApi.TrackingDataType.Head);
        // the offset you originally used for a 1.80 m avatar:
        const float defaultHeight = 1.80f;
        const float defaultOffset = 0.60f;

        // grab the current avatar’s eye-height
        float avatarHeight = Networking.LocalPlayer.GetAvatarEyeHeightAsMeters();

        // compute a scaled offset
        float scaledOffset = defaultOffset * (avatarHeight / defaultHeight);

        // apply it
        transform.position = new Vector3(
            transform.position.x,
            trackingData.position.y - scaledOffset,
            transform.position.z
        );
        syncPosition = transform.position;

        syncRotation = transform.rotation;
        RequestSerialization();
    }

    public override void OnDeserialization()
    {
        // base.OnDeserialization();

        Debug.Log("Deserialization called for Revolver");

        if (_l_ourShotTarget != ourShotTarget)
        {
            if (ourShotTarget != -1 && myGame.currentGameMode == 1)
            {
                if (ourShotTarget >= playerSeats.Length || ourShotTarget < 0)
                {
                    Debug.LogWarning("Invalid shot target index: " + ourShotTarget);

                }
                else
                {
                    int plrId = Networking.GetOwner(playerSeats[ourShotTarget].gameObject).playerId;
                    if (!nextShotWillKill && plrId == Networking.LocalPlayer.playerId)
                    {
                        _pod.GetLocalPlayerScript()._StoreShowdownBulletDodge();
                    }
                }
            }
        }

        if (_l_customizationId != customizationId ||
            _l_customizationSkinId != customizationSkinId)
        {
            // Debug.Log("Customization ID changed: " + customizationId);
            ChangeCustomization(customizationId, customizationSkinId);
            _l_customizationId = customizationId;
            _l_customizationSkinId = customizationSkinId;
        }
        // TODO: NETWORK CALLABLE IMPLEMENTATION
        // if (_l_ourShotTarget != ourShotTarget)
        // {
        //     _l_ourShotTarget = ourShotTarget;
        //     if (ourShotTarget != -1 && myGame.currentGameMode == 1)
        //     {
        //         int plrId = Networking.GetOwner(playerSeats[ourShotTarget].gameObject).playerId;
        //         // Debug.Log("This gun's target is: " + plrId + " thats me?: " + (plrId == Networking.LocalPlayer.playerId));

        //         if (nextShotWillKill && plrId == Networking.LocalPlayer.playerId)
        //         {
        //             Debug.Log("We are the target! QD Challenger, and QD target: " + myGame.qdChallenger + " " + myGame.qdChallengeTarget + " " + myGame.quickdrawCalled);
        //             if (myGame.quickdrawCalled)
        //             {
        //                 if (myGame.qdChallenger == myGame.GetLocalPlayerSeatID() || myGame.qdChallengeTarget == myGame.GetLocalPlayerSeatID())
        //                 {
        //                     // _pod.GetLocalPlayerScript()._StoreShowdownKill();
        //                     // _pod.GetLocalPlayerScript()._Die();
        //                 }
        //                 else
        //                 {
        //                     Debug.Log("... but we are not involved in this QD so we are not dying.");
        //                 }
        //             }
        //             else
        //             {
        //                 // if (!myGame.qdChallengerDead && !myGame.qdChallengeTargetDead)
        //                     // _pod.GetLocalPlayerScript()._Die();
        //             }
        //         }
        //         else if (!nextShotWillKill && plrId == Networking.LocalPlayer.playerId)
        //         {
        //             _pod.GetLocalPlayerScript()._StoreShowdownBulletDodge();
        //         }
        //     }
        // }
        // if (shotFired != _l_shotFired)
        // {
        //     Debug.Log("Got shot fired: " + shotFired);
        //     if (shotFired)
        //     {
        //         transform.position = syncPosition;
        //         transform.rotation = syncRotation;

        //         // ShootRemote();
        //         // if (_pod.GetLocalPlayerScript()._AmITheMasterOfMyOwnTable())
        //         // {
        //         //     myGame._ShotFired(nextShotWillKill, ourShotTarget, shotMyself, Networking.GetOwner(gameObject).playerId);
        //         // }
        //     }
        //     _l_shotFired = shotFired;
        // }



        if (forceFireActivated != _l_forceFireActivated)
        {
            _l_forceFireActivated = forceFireActivated;
            _PullTheTrigger(false);
        }

        if (particleCustomizationId != _l_particleCustomizationId)
        {
            _l_particleCustomizationId = particleCustomizationId;
            ChangeParticleCustomization(particleCustomizationId);
        }

        if (!Networking.IsOwner(gameObject))
        {
            if (syncPosition != _l_syncPosition)
            {
                transform.position = syncPosition;
                _l_syncPosition = syncPosition;
            }

            if (syncRotation != _l_syncRotation)
            {
                transform.rotation = syncRotation;
                _l_syncRotation = syncRotation;
            }
        }

    }

    private void DoShotTrail()
    {
        if (shotVisual != null)
        {

            shotVisualAnimator.Play("FadeOutBulletTrail", 0, 0);

            shotVisualLineRenderer.positionCount = 2;

            Vector3 startPoint = gunTransform.position;
            Vector3 endPoint = gunTransform.position + (gunTransform.transform.forward * 5f);

            shotVisualLineRenderer.SetPosition(0, startPoint);
            shotVisualLineRenderer.SetPosition(1, endPoint);
        }
    }

    [NetworkCallable]
    public void ShootRemote(Vector3 gunPos, Quaternion gunRot)
    {
        // if (!shotFired)
        // {
        //     // Debug.Log("No shot fired?");
        //     return;
        // }
        // Debug.Log("Remote player shot the gun!");
        if (nextShotWillKill)
        {

            if (shotSoundOverride != null) shotSoundOverride.Play();
            else myAudioSource.PlayOneShot(clips[1]);

            shotParticles.Play();
            if (shotAnimatorExtra != null)
            {
                shotAnimatorExtra.SetTrigger("Fire");
            }
            shotAnimator.SetTrigger("Muzzle");
            if (Utilities.IsValid(myGame) && myGame.quickdrawCalled)
            {
                transform.position = gunPos;
                transform.rotation = gunRot;
                DoShotTrail();
            }
        }
        else
            myAudioSource.PlayOneShot(clips[0]);

        flyingBackToStart = true;

        SendCustomEventDelayedSeconds("ReturnGunBack", 2f);
    }


    public void Shoot()
    {
        // Debug.Log("BLAM!");

        if (shotFired || Networking.GetOwner(gameObject) != Networking.LocalPlayer)
        {
            // Debug.Log("Shot already fired or not owner, returning");
            Debug.Log("Shot already fired, returning");
            return;
        }

        shotFired = true;
        _l_shotFired = true;

        bool wantedKill = false;

        if (nextShotWillKill)
        {

            if (shotSoundOverride != null) shotSoundOverride.Play();
            else myAudioSource.PlayOneShot(clips[1]);

            if (waitingForQDShot)
            {
                ourShotTarget = GetRaycastTarget();
                // int shotTargetPlayerID = myGame.GetPlayerIDForSeat(ourShotTarget);
                // if (shotTargetPlayerID != -1 && (shotTargetPlayerID == myGame.qdChallenger || shotTargetPlayerID == myGame.qdChallengeTarget))
                //     _pod.GetPlayerScript(VRCPlayerApi.GetPlayerById(shotTargetPlayerID)).SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");
                aimingLoopEnabled = false;
                waitingForQDShot = false;
                QDShotTime = myGame.GetNormalizedServerTime();
                syncPosition = transform.position;
                syncRotation = transform.rotation;
                DoShotTrail();

            }
            Debug.Log("We killed: " + ourShotTarget);
            if (ourShotTarget != -1)
            {
                int plrId = Networking.GetOwner(playerSeats[ourShotTarget].gameObject).playerId;
                Debug.Log("Player ID is " + plrId + ", wanted is: " + myGame.wantedPlayerIDAtStartOfGame);
                if (plrId == (int)myGame.GetCurrentBountyStatus()[0] && nextShotWillKill && !(isSelfShot || ourShotTarget == localSeatIndex))
                {
                    _pod.GetLocalPlayerScript().persistentStats._StoreWantedKill((int)myGame.GetCurrentBountyStatus()[1]);
                    wantedKill = true;
                }
                if (nextShotWillKill && myGame.gunslingerCalled)
                {
                    _pod.GetLocalPlayerScript().persistentStats._StoreGunslingerKill();
                }
                if ((myGame.gunslingerCalled || myGame.showdownCalled || gunslingerActive) && Networking.GetOwner(playerSeats[ourShotTarget].gameObject).displayName == "aRkker")
                {
                    _pod.GetLocalPlayerScript().persistentStats._StoreSheriffKill();
                }
            }
            // shotAnimator.SetTrigger("Muzzle");
            shotParticles.Play();
            if (shotAnimatorExtra != null)
            {
                shotAnimatorExtra.SetTrigger("Fire");
            }

            if (isSelfShot || ourShotTarget == localSeatIndex)
            {
                Networking.SetOwner(Networking.LocalPlayer, ghostPhotoCapture.gameObject);
                ghostPhotoCapture.CaptureHalfBodyPhoto(Networking.LocalPlayer.playerId);
                shotMyself = true;
                Debug.Log("SELF SHOT!");
                // _pod.GetLocalPlayerScript().SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "Die");

                if (!isSelfShot && (gunslingerActive || showDownActive))
                {
                    _pod.GetLocalPlayerScript()._StoreLoveThyself();
                }
            }
            else
            {
                _pod.GetLocalPlayerScript().ScheduleNewBullet();
                _pod.GetLocalPlayerScript()._StoreShowdownKill();
                shotMyself = false;
            }
        }
        else
        {
            // Debug.Log("Gun is empty!");
            if (myGame != null)
            {
                if (myGame.devilCardshots > 0 && myGame.currentGameMode == 0)
                {
                    _pod.GetLocalPlayerScript().persistentStats._StoreDevilsCardSurvive();
                }
            }
            myAudioSource.PlayOneShot(clips[0]);
            _pod.GetLocalPlayerScript().ShotDidNotKill();
        }
        _pod.GetLocalPlayerScript().ShotFired(nextShotWillKill, shotMyself, ourShotTarget, wantedKill);

        RequestSerialization();

        myGame.SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Owner, "ShotFired", nextShotWillKill, ourShotTarget, shotMyself, Networking.GetOwner(gameObject).playerId);

        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.Others, "ShootRemote", transform.position, transform.rotation);
        // if (_pod.GetLocalPlayerScript()._AmITheMasterOfMyOwnTable())
        // {
        //     myGame._ShotFired(nextShotWillKill, ourShotTarget, shotMyself, Networking.GetOwner(gameObject).playerId);
        // }

        if (Networking.IsOwner(gameObject))
        {
            OnDeserialization();
        }


        SendCustomEventDelayedSeconds("ReturnGunBack", 2f);
        _pod.GetLocalPlayerScript().PullTheTrigger();
        showDownActive = false;

    }

    private int GetRaycastTarget()
    {
        // We need to cast a ray from the gun tip and see if it hits any objects called QDHitbox. If so, we need to check it's parents name for what seat ID it is
        Ray ray = new Ray(gunTransform.position, gunTransform.forward);
        RaycastHit hit;

        // Debug-draw the ray
        // Debug.DrawRay(ray.origin, ray.direction * 100f, Color.red, 1f);

        // only on the layer 27, which is for the hitboxes
        if (Physics.Raycast(ray, out hit, 20f, 1 << 27))
        {
            // if (hit.collider.gameObject.name == "QDHitbox")
            // {
            //     int hitSeat = int.Parse(hit.collider.gameObject.transform.parent.parent.name.Substring(4)) - 1;
            //     Debug.Log("Hit seat ID: " + hitSeat);
            //     if (Utilities.IsValid(myGame))
            //     {
            //         if (myGame.seatStatuses[hitSeat])
            //         {
            //             return hitSeat;
            //         }
            //     }
            // }

            if (myGame.qdChallenger == _pod.GetLocalPlayerScript().mySeatID)
            {
                if (hit.collider.gameObject.name == "Challengee Hitbox")
                {
                    int hitSeat = myGame.qdChallengeTarget;
                    if (Utilities.IsValid(myGame))
                    {
                        if (myGame.seatStatuses[hitSeat])
                        {
                            return hitSeat;
                        }
                    }
                }
                else if (hit.collider.gameObject.name == "Challenger Hitbox")
                {
                    // We hit our own hitbox, so we are the challenger
                    int hitSeat = myGame.qdChallenger;
                    if (Utilities.IsValid(myGame))
                    {
                        if (myGame.seatStatuses[hitSeat])
                        {
                            return hitSeat;
                        }
                    }
                }
            }
            if (myGame.qdChallengeTarget == _pod.GetLocalPlayerScript().mySeatID)
            {
                if (hit.collider.gameObject.name == "Challenger Hitbox")
                {
                    int hitSeat = myGame.qdChallenger;
                    if (Utilities.IsValid(myGame))
                    {
                        if (myGame.seatStatuses[hitSeat])
                        {
                            return hitSeat;
                        }
                    }
                }
                else if (hit.collider.gameObject.name == "Challengee Hitbox")
                {
                    // We hit our own hitbox, so we are the challenge target
                    int hitSeat = myGame.qdChallengeTarget;
                    if (Utilities.IsValid(myGame))
                    {
                        if (myGame.seatStatuses[hitSeat])
                        {
                            return hitSeat;
                        }
                    }
                }
            }
        }


        return -1;
    }

    public void ResetGunTimed()
    {
        shotFired = false;
        _l_shotFired = false;
        _l_ourShotTarget = -1;
        ourShotTarget = -1;
        _pod.GetLocalPlayerScript().ResetGunShowdown();
        gunslingerActive = false;
        showDownActive = false;
        forceFireActivated = false;
        QDShotTime = -1.0;
        RequestSerialization();
    }

    public void MakeGunQDPlayable()
    {
        crosshairCanvas.SetActive(false);
        GetComponent<Collider>().enabled = true;
        GetComponent<VRC_Pickup>().pickupable = true;
        waitingForQDShot = true;
        // aimingLoopEnabled = true;
        isSelfShot = false;
        flyingBackToStart = false;
        if (!Networking.LocalPlayer.IsUserInVR())
        {
            GetComponent<VRC_Pickup>().orientation = VRC_Pickup.PickupOrientation.Gun;
            GetComponent<VRC_Pickup>().ExactGun = otherGunGrip;
        }
        else
        {
            GetComponent<VRC_Pickup>().orientation = VRC_Pickup.PickupOrientation.Grip;
        }
        // _AimingLoop();
    }

    public void ReturnGunBack()
    {
        GetComponent<Collider>().enabled = false;

        GetComponent<VRC_Pickup>().Drop();
        GetComponent<VRC_Pickup>().pickupable = true;

        aimingLoopEnabled = false;
        crosshairCanvas.SetActive(false);


        flyingBackToStart = true;
        crosshairCanvas.SetActive(false);
        indicator.SetActive(false);
        SendCustomEventDelayedSeconds("ResetGunTimed", 7f);
    }

    public void NetworkedChamberSpin()
    {
        SendCustomNetworkEvent(VRC.Udon.Common.Interfaces.NetworkEventTarget.All, "SpinTheChamber");
    }

    public void SpinTheChamber()
    {
        myAudioSource.PlayOneShot(chamberSpinClip);
    }

    void Update()
    {
        if (!flyingToAutoFirePoint && !flyingBackToStart && !flyingToQDPosition) return;

        if (flyingToQDPosition)
        {
            transform.position = Vector3.MoveTowards(transform.position, qdPosition, moveSpeed * Time.deltaTime);
            transform.rotation = Quaternion.RotateTowards(transform.rotation, qdRotation, rotationSpeed * Time.deltaTime);

            if (Vector3.Distance(transform.position, qdPosition) < 0.01f && Quaternion.Angle(transform.rotation, qdRotation) < 1f)
            {
                flyingToQDPosition = false;
                // Snap to exact position to avoid floating point drift
                transform.position = qdPosition;
                transform.rotation = qdRotation;
            }
        }

        if (flyingToAutoFirePoint)
        {

            // Smoothly move towards the deck's position at a consistent speed
            transform.position = Vector3.MoveTowards(transform.position, desktopAutoFirePosition, moveSpeed * Time.deltaTime);

            // Smoothly rotate towards the deck's rotation at a consistent speed
            // transform.localRotation = Quaternion.RotateTowards(transform.localRotation, desktopAutoFireRotation, rotationSpeed * Time.deltaTime);
            transform.rotation = Quaternion.RotateTowards(transform.rotation, desktopAutoFireRotation, rotationSpeed * Time.deltaTime);

            if (Vector3.Distance(transform.position, desktopAutoFirePosition) < 0.01f && Quaternion.Angle(transform.rotation, desktopAutoFireRotation) < 1f)
            {
                // Debug.Log("We are here!");
                flyingToAutoFirePoint = false;
                if (!showDownActive)
                    Shoot();
            }
        }

        if (flyingBackToStart)
        {
            // Smoothly move towards the deck's position at a consistent speed
            transform.position = Vector3.MoveTowards(transform.position, startingPosition, moveSpeed * Time.deltaTime);

            // Smoothly rotate towards the deck's rotation at a consistent speed
            transform.rotation = Quaternion.RotateTowards(transform.rotation, startingRotation, rotationSpeed * Time.deltaTime);

            if (Vector3.Distance(transform.position, startingPosition) < 0.01f && Quaternion.Angle(transform.rotation, startingRotation) < 1f)
            {
                flyingBackToStart = false;
                GetComponent<Collider>().enabled = false;
                CleanGun();
                if (Networking.IsOwner(gameObject))
                {
                    syncPosition = transform.position;
                    syncRotation = transform.rotation;
                    RequestSerialization();
                }
            }
        }
    }

    internal void FireShotScheduled()
    {
        if (shotFired) return;
        // if (nextShotWillKill)
        ourShotTarget = GetBestTargetIndex();
        _pod.GetLocalPlayerScript().StoreTargetForShot(ourShotTarget, nextShotWillKill);
        RequestSerialization();

        // else
        // Debug.Log("Our shot was not lethal, not registering target");
        aimingLoopEnabled = false;
        Shoot();
    }

    internal void ShowdownTimerExpired()
    {
        GetComponent<VRC_Pickup>().orientation = VRC_Pickup.PickupOrientation.Gun;
        // flyingToAutoFirePoint = true;
        _PullTheTrigger(false);
        indicator.SetActive(false);
        GetComponent<Collider>().enabled = false;
        GetComponent<VRC_Pickup>().pickupable = false;
        _pod.GetLocalPlayerScript().GunPickedUpForShowdown();
        aimingLoopEnabled = true;
        isSelfShot = true;
        _AimingLoop();
    }

    internal void ReturnGunBackImmediately()
    {
        GetComponent<Collider>().enabled = false;

        GetComponent<VRC_Pickup>().Drop();
        GetComponent<VRC_Pickup>().pickupable = true;

        aimingLoopEnabled = false;
        crosshairCanvas.SetActive(false);

        crosshairCanvas.SetActive(false);
        indicator.SetActive(false);

        transform.position = startingPosition;
        transform.rotation = startingRotation;

        flyingBackToStart = false;
        syncPosition = transform.position;
        syncRotation = transform.rotation;

        RequestSerialization();
        SendCustomEventDelayedSeconds("ResetGunTimed", 7f);

    }

    internal void ResetGunQD()
    {
        GetComponent<VRC_Pickup>().orientation = VRC_Pickup.PickupOrientation.Gun;

        SendCustomEventDelayedSeconds("ReturnGunBack", 2f);
    }
}
